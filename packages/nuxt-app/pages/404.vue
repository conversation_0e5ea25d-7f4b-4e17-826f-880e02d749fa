<template>
  <div class="not-found-page">
    <div class="background-pattern">
      <div class="grid-pattern" />
      <div class="floating-elements">
        <div class="element element-1" />
        <div class="element element-2" />
        <div class="element element-3" />
      </div>
    </div>

    <div class="not-found-container">
      <div class="not-found-content">
        <!-- 404 Animation -->
        <div class="number-display">
          <div class="number">4</div>
          <div class="number-zero">
            <Icon name="material-symbols:search" size="120" class="zero-icon" />
          </div>
          <div class="number">4</div>
        </div>

        <!-- Content -->
        <div class="content-section">
          <h1 class="title">Oops! Page Not Found</h1>
          <p class="description">
            The page you're looking for seems to have vanished into the digital
            void. Don't worry though - let's get you back to exploring amazing
            AI stories!
          </p>

          <!-- Search Suggestions -->
          <div class="suggestions">
            <h3 class="suggestions-title">What were you looking for?</h3>
            <div class="suggestion-chips">
              <button
                v-for="suggestion in popularSuggestions"
                :key="suggestion.label"
                class="suggestion-chip"
                @click="navigateTo(suggestion.path)"
              >
                <Icon :name="suggestion.icon" size="16" />
                {{ suggestion.label }}
              </button>
            </div>
          </div>
        </div>

        <!-- Actions -->
        <div class="actions">
          <button class="btn btn-primary" @click="goHome">
            <Icon name="material-symbols:home" size="20" />
            Go Home
          </button>

          <button class="btn btn-secondary" @click="goBack">
            <Icon name="material-symbols:arrow-back" size="20" />
            Go Back
          </button>

          <button class="btn btn-tertiary" @click="reportIssue">
            <Icon name="material-symbols:bug-report" size="20" />
            Report Issue
          </button>
        </div>

        <!-- Help Section -->
        <div class="help-section">
          <div class="help-card">
            <div class="help-icon">
              <Icon name="material-symbols:help-outline" size="24" />
            </div>
            <div class="help-content">
              <h4>Still can't find what you need?</h4>
              <p>Try these helpful tips:</p>
              <ul>
                <li>Check the URL spelling</li>
                <li>Use the search function</li>
                <li>Browse our story categories</li>
                <li>Contact our support team</li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 使用品牌配置
const { brandingConfig } = useBranding()
const brand = brandingConfig.value

// SEO Meta
useHead({
  title: `Page Not Found - ${brand.websiteTitle}`,
  meta: [
    { name: 'robots', content: 'noindex, nofollow' },
    {
      name: 'description',
      content: 'The page you are looking for could not be found.',
    },
  ],
})

// 页面元数据
definePageMeta({
  layout: false,
})

// 热门建议
const popularSuggestions = [
  { label: 'Stories', path: '/stories', icon: 'material-symbols:auto-stories' },
  { label: 'Home', path: '/', icon: 'material-symbols:home' },
  {
    label: 'Profile',
    path: '/user/profile',
    icon: 'material-symbols:account-circle',
  },
  {
    label: 'Settings',
    path: '/user/settings',
    icon: 'material-symbols:settings',
  },
]

// 页面操作
const goHome = () => {
  navigateTo('/')
}

const goBack = () => {
  if (typeof window !== 'undefined' && window.history.length > 1) {
    window.history.back()
  } else {
    navigateTo('/')
  }
}

const reportIssue = () => {
  const subject = encodeURIComponent('404 Page Not Found Report')
  const body = encodeURIComponent(`
Hello Support Team,

I encountered a 404 error when trying to access:
URL: ${typeof window !== 'undefined' ? window.location.href : 'N/A'}
Time: ${new Date().toISOString()}

Please investigate this issue.

Best regards
  `)

  window.open(`mailto:${brand.supportEmail}?subject=${subject}&body=${body}`)
}

// 错误追踪
if (typeof window !== 'undefined') {
  console.warn('404 Page accessed:', {
    url: window.location.href,
    referrer: document.referrer,
    timestamp: new Date().toISOString(),
  })
}
</script>

<style lang="less" scoped>
.not-found-page {
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
}

.background-pattern {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  .grid-pattern {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: linear-gradient(
        rgba(var(--accent-color-rgb, 74, 144, 226), 0.03) 1px,
        transparent 1px
      ),
      linear-gradient(
        90deg,
        rgba(var(--accent-color-rgb, 74, 144, 226), 0.03) 1px,
        transparent 1px
      );
    background-size: 50px 50px;
    animation: gridMove 20s linear infinite;
  }

  .floating-elements {
    position: absolute;
    width: 100%;
    height: 100%;

    .element {
      position: absolute;
      background: var(--accent-color);
      border-radius: 50%;
      opacity: 0.1;
      animation: float 8s ease-in-out infinite;

      &.element-1 {
        width: 100px;
        height: 100px;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.element-2 {
        width: 60px;
        height: 60px;
        top: 70%;
        right: 15%;
        animation-delay: 3s;
      }

      &.element-3 {
        width: 80px;
        height: 80px;
        bottom: 20%;
        left: 30%;
        animation-delay: 6s;
      }
    }
  }
}

.not-found-container {
  position: relative;
  z-index: 2;
  max-width: 800px;
  width: 90%;
  padding: 0 20px;
}

.not-found-content {
  background: var(--bg-secondary);
  border-radius: 24px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 60px var(--shadow-color);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

.number-display {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 20px;
  margin-bottom: 40px;

  .number {
    font-size: 120px;
    font-weight: 900;
    color: var(--accent-color);
    line-height: 1;
    text-shadow: 0 4px 12px rgba(var(--accent-color-rgb, 74, 144, 226), 0.3);
  }

  .number-zero {
    position: relative;
    width: 120px;
    height: 120px;
    display: flex;
    align-items: center;
    justify-content: center;

    .zero-icon {
      color: var(--accent-color);
      animation: spin 3s linear infinite;
    }
  }
}

.content-section {
  margin-bottom: 40px;

  .title {
    font-size: 36px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
    line-height: 1.2;
  }

  .description {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 40px;
    max-width: 500px;
    margin-left: auto;
    margin-right: auto;
  }
}

.suggestions {
  margin-bottom: 40px;

  .suggestions-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 16px;
  }

  .suggestion-chips {
    display: flex;
    gap: 12px;
    justify-content: center;
    flex-wrap: wrap;

    .suggestion-chip {
      display: flex;
      align-items: center;
      gap: 8px;
      padding: 10px 16px;
      background: var(--bg-tertiary);
      border: 1px solid var(--border-color);
      border-radius: 20px;
      color: var(--text-secondary);
      font-size: 14px;
      cursor: pointer;
      transition: all 0.2s ease;

      &:hover {
        background: var(--accent-bg);
        border-color: var(--accent-color);
        color: var(--accent-color);
        transform: translateY(-2px);
      }
    }
  }
}

.actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;

  .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 120px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &.btn-primary {
      background: var(--accent-color);
      color: var(--text-on-accent);

      &:hover {
        background: var(--accent-hover);
      }
    }

    &.btn-secondary {
      background: var(--bg-hover);
      color: var(--text-primary);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--bg-tertiary);
        border-color: var(--accent-color);
      }
    }

    &.btn-tertiary {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }
    }
  }
}

.help-section {
  border-top: 1px solid var(--border-color);
  padding-top: 30px;

  .help-card {
    display: flex;
    gap: 16px;
    background: var(--bg-tertiary);
    border: 1px solid var(--border-color);
    border-radius: 16px;
    padding: 24px;
    text-align: left;

    .help-icon {
      flex-shrink: 0;
      width: 48px;
      height: 48px;
      background: var(--accent-bg);
      border-radius: 12px;
      display: flex;
      align-items: center;
      justify-content: center;
      color: var(--accent-color);
    }

    .help-content {
      flex: 1;

      h4 {
        font-size: 16px;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: 8px;
      }

      p {
        font-size: 14px;
        color: var(--text-secondary);
        margin-bottom: 12px;
      }

      ul {
        list-style: none;
        padding: 0;
        margin: 0;

        li {
          position: relative;
          padding-left: 16px;
          margin-bottom: 4px;
          font-size: 14px;
          color: var(--text-tertiary);

          &:before {
            content: '→';
            position: absolute;
            left: 0;
            color: var(--accent-color);
          }
        }
      }
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .not-found-content {
    padding: 40px 24px;

    .number-display {
      gap: 10px;

      .number {
        font-size: 80px;
      }

      .number-zero {
        width: 80px;
        height: 80px;

        .zero-icon {
          width: 60px;
          height: 60px;
        }
      }
    }

    .title {
      font-size: 28px;
    }

    .description {
      font-size: 15px;
    }
  }

  .actions {
    flex-direction: column;

    .btn {
      width: 100%;
      justify-content: center;
    }
  }

  .help-section .help-card {
    flex-direction: column;
    text-align: center;

    .help-icon {
      margin: 0 auto;
    }
  }
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0px) rotate(0deg);
  }
  50% {
    transform: translateY(-20px) rotate(180deg);
  }
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes gridMove {
  0% {
    transform: translate(0, 0);
  }
  100% {
    transform: translate(50px, 50px);
  }
}
</style>

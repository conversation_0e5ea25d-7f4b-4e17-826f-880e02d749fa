<template>
  <div
    class="app-mobile"
    :class="{
      rotating: isRotating,
    }"
    :style="isEnabled ? 'cursor: move' : ''"
  >
    <RouterTransition />
    <DiamondUsedUpModal
      :visible="chatEventsStore.paymentRequired"
      @update:visible="handleDiamondModalClose"
      @leave="handleLeaveGame"
    />
    <RechargeModal />
    <!-- <GenderModal v-model:visible="genderStore.showModal" @select="genderStore.handleGenderSelect" /> -->

    <!-- Guest用户试玩弹窗 -->
    <!-- <DemoModal
      :visible="showGuestDemoModal"
      :title="guestDemoTitle"
      @update:visible="showGuestDemoModal = $event"
      @login="handleGuestLogin"
      @signup-later="handleGuestSignupLater"
    /> -->
  </div>
</template>

<script setup lang="ts">
import { onMounted, watch, ref, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import RouterTransition from '@/mobile/components/RouterTransition.vue'
import GenderModal from '@/mobile/components/GenderModal.vue'
import RechargeModal from '@/mobile/components/RechargeModal.vue'
import DiamondUsedUpModal from '@/mobile/components/DiamondUsedUpModal.vue'
import DemoModal from '@/mobile/components/DemoModal.vue'
import { useGenderStore } from '@/store/gender'
import { useChatEventsStore } from '@/store/chat-events'
import { useRechargeStore } from '@/store/recharge'
import { useGlobalAvatarPolling } from '@/mobile/composables/useGlobalAvatarPolling'
import { useDeviceRotation } from '@/mobile/composables/useDeviceRotation'
import { useForcePortrait } from '@/mobile/composables/useForcePortrait'
import { useShareFeature } from '@/mobile/views/chat2/composables/useShare'
import { useRouteAdaptation } from '@/composables/useRouteAdaptation'
import { isAndroidWebView } from '@/utils/isAndroidWebView'
import { monitorKeyboardEvents } from '@/utils/util'
import { useUserStore } from '@/store/user'

// Initialize global polling

const AppName = import.meta.env.VITE_APP_NAME || 'Playshot'

const route = useRoute()
const router = useRouter()
const genderStore = useGenderStore()
const userStore = useUserStore()

const chatEventsStore = useChatEventsStore()
const rechargeStore = useRechargeStore()
const { reportShareDataByLink } = useShareFeature()

// Guest用户试玩弹窗状态
const showGuestDemoModal = ref(false)
const guestDemoTitle = ref('Welcome! Try our demo features!')
// 标志：用户是否已经主动关闭过Demo弹窗
const userDismissedDemo = ref(false)

// 处理钻石用完模态框关闭
const handleDiamondModalClose = (value: boolean) => {
  if (!value) {
    chatEventsStore.paymentRequired = false
  }
}

// 处理guest用户试玩弹窗事件
const handleGuestLogin = () => {
  userDismissedDemo.value = true
  showGuestDemoModal.value = false
  router.push('/user/login')
}

const handleGuestSignupLater = () => {
  userDismissedDemo.value = true
  showGuestDemoModal.value = false
  console.log('Guest user chose to sign up later')
}

// 处理离开游戏
const handleLeaveGame = () => {
  // 返回主页
  router.push('/')
}

// Monitor route parameter changes
watch(
  () => route.query,
  () => {
    reportShareDataByLink()
  },
  { immediate: true },
)

// 检查是否在首页
const isHomePage = computed(() => {
  return route.path === '/' || route.path === '/stories'
})

// 监听用户状态变化，当用户不再是guest时隐藏DemoModal
// watch(
//   () => userStore.userInfo?.role,
//   (newRole, oldRole) => {
//     if (oldRole === 'guest' && newRole !== 'guest') {
//       showGuestDemoModal.value = false
//       // 用户登录成功，重置关闭标志
//       userDismissedDemo.value = false
//     } else if (
//       newRole === 'guest' &&
//       !showGuestDemoModal.value &&
//       isHomePage.value &&
//       !userDismissedDemo.value
//     ) {
//       // 用户变为guest状态且弹窗未显示且在首页且用户未主动关闭过，则显示弹窗
//       setTimeout(() => {
//         showGuestDemoModal.value = true
//       }, 500)
//     }
//   },
//   { immediate: false }
// )

onMounted(async () => {
  const checkAndShowLayout = () => {
    const layoutElement = document.querySelector('.app-mobile') as HTMLElement
    if (layoutElement) {
      layoutElement.classList.add('loaded')
      layoutElement.style.opacity = '1'
      console.log('✅ Mobile Layout 已显示')
    }
  }

  // 检查是否在微前端环境中
  const isInIframe = typeof window !== 'undefined' && window.self !== window.top
  const isChatRoute = route.path.startsWith('/chat')

  if (isInIframe && isChatRoute) {
    // 在微前端的 chat 环境中，延迟显示以避免闪现
    console.log('🔄 微前端 chat 环境，延迟显示 Mobile Layout')

    // 等待更长时间确保路由完全准备就绪
    setTimeout(checkAndShowLayout, 300)

    // 备用方案：更长的延迟显示
    setTimeout(checkAndShowLayout, 800)
  } else {
    // 非微前端环境或非 chat 路由，正常显示
    setTimeout(checkAndShowLayout, 50)
    setTimeout(checkAndShowLayout, 500)
  }

  // 初始化键盘事件监听，确保在软键盘弹起和收起时触发相应事件
  monitorKeyboardEvents()
  if (AppName === 'ReelPlay') {
    useGlobalAvatarPolling()
  }

  // 后台加载用户信息，不阻塞页面显示
  if (userStore.isAuthenticated) {
    userStore.getUserInfo().catch((error) => {
      console.warn('Failed to load user info:', error)
    })
  }

  // if (userStore.userInfo?.role === 'guest' && isHomePage.value && !userDismissedDemo.value) {
  //   // 延迟一点时间确保页面完全加载
  //   setTimeout(() => {
  //     showGuestDemoModal.value = true
  //   }, 500)
  // }
})

// Enable device rotation feature
const { isRotating, isEnabled } = useDeviceRotation()

// Enable force portrait mode
useForcePortrait()
</script>

<style lang="less">
@import '@/mobile/styles/adaptation.less';
@import '@/mobile/styles/index.less';

#app {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
}

.app-mobile {
  height: calc(var(--vh, 1vh) * 100);
  // Disable text selection
  -webkit-user-select: none;
  user-select: none;
  // Disable element dragging
  -webkit-user-drag: none;
  // Set touch operations
  touch-action: manipulation;
  // Set overflow handling
  overflow-x: hidden;
  // Optimize scrolling experience
  -webkit-overflow-scrolling: touch;
  // Enable hardware acceleration
  -webkit-transform: translateZ(0);
  transform: translateZ(0);

  // 确保移动端布局始终可见
  opacity: 1 !important;

  // 如果有 loaded 类，添加过渡效果
  &.loaded {
    opacity: 1;
    transition: opacity 0.3s ease;
  }

  img {
    -webkit-user-drag: none;
    user-select: none;
  }

  // 设备旋转效果样式
  // &.rotating {
  //   transition: transform 0.6s cubic-bezier(0.23, 1, 0.32, 1);
  // }
}
</style>

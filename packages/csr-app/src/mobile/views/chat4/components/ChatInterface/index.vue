<template>
  <div class="chat-interface" ref="messagesContainer">
    <!-- 时间显示 -->
    <div class="message-timestamp">
      {{ formatCurrentTime() }}
    </div>

    <!-- 动态渲染聊天消息 -->
    <div
      v-for="message in messages"
      :key="message.id"
      class="message-group"
      :class="{
        'user-group': message.type === 'user',
        'gift-group': message.type === 'gift',
        'system-group': message.type === 'system',
        'short': message.content.length < 30,
      }"
    >
      <!-- 角色消息 -->
      <template v-if="message.type === 'actor'">
        <div class="actor-avatar">
          <img :src="actorAvatar" :alt="actorName" />
        </div>
        <div
          class="message-bubble left"
          :class="{ short: message.content.length < 30 }"
        >
          <div class="bubble-content">{{ message.content }}</div>
        </div>
      </template>

      <!-- 礼物消息 -->
      <template v-else-if="message.type === 'gift' && message.giftData">
        <GiftMessage
          :gift-data="message.giftData.gift"
          :quantity="message.giftData.quantity"
          :user-avatar="message.giftData.userAvatar"
        />
        <div class="user-avatar">
          <img
            :src="
              message.giftData.userAvatar ||
              'https://cdn.magiclight.ai/assets/playshot/default-avatar.png'
            "
            alt="User"
          />
        </div>
      </template>

      <!-- 用户消息 -->
      <div v-else-if="message.type === 'user'" class="message-bubble right">
        <div class="bubble-content">{{ message.content }}</div>
      </div>

      <!-- 系统消息 -->
      <div v-else-if="message.type === 'system'" class="system-message">
        <div class="system-content">
          <div class="system-text">{{ message.content }}</div>
        </div>
      </div>
    </div>
  </div>

  <!-- 如果没有消息，显示默认内容 -->
  <!-- <div v-if="messages.length === 0" class="empty-state">
      <div class="message-group">
        <div class="message-bubble left">
          <div class="bubble-content">Are you there?</div>
        </div>
      </div>
    </div> -->
</template>

<script setup lang="ts">
import { ref, nextTick, watch } from 'vue'
import GiftMessage from '../GiftMessage/index.vue'

interface Message {
  id: string
  type: 'actor' | 'user' | 'gift' | 'system'
  content: string
  timestamp: number
  giftData?: {
    gift: any
    quantity: number
    userAvatar?: string
  }
}

interface Props {
  messages: Message[]
  actorName?: string
  actorAvatar?: string
}

const props = withDefaults(defineProps<Props>(), {
  actorName: 'Tsunade',
  actorAvatar: 'https://cdn.magiclight.ai/assets/playshot/tsunade-avatar.jpg',
})

const messagesContainer = ref<HTMLElement>()

// 格式化当前时间为英文格式
const formatCurrentTime = () => {
  const now = new Date()
  return now.toLocaleString('en-US', {
    weekday: 'short',
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    hour12: true,
  })
}

// 自动滚动到底部
const scrollToBottom = () => {
  nextTick(() => {
    if (messagesContainer.value) {
      const container = messagesContainer.value
      // 使用 scrollTo 方法，提供更好的滚动控制
      container.scrollTo({
        top: container.scrollHeight,
        behavior: 'smooth', // 平滑滚动
      })
    }
  })
}

// 监听消息变化，自动滚动到底部
watch(
  () => props.messages,
  (newMessages, oldMessages) => {
    // 只有在消息数量增加时才滚动，避免不必要的滚动
    if (newMessages.length > (oldMessages?.length || 0)) {
      scrollToBottom()
    }
  },
  { deep: true, immediate: false },
)

// 组件挂载后也滚动到底部（如果有初始消息）
watch(
  messagesContainer,
  (container) => {
    if (container && props.messages.length > 0) {
      // 延迟一下确保DOM完全渲染
      setTimeout(() => {
        scrollToBottom()
      }, 100)
    }
  },
  { immediate: true },
)
</script>

<style lang="less" scoped>
.chat-interface {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 20px;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 10px;
  box-sizing: border-box;

  /* 自定义滚动条样式 */
  &::-webkit-scrollbar {
    width: 4px;
  }

  &::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
  }

  &::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 2px;

    &:hover {
      background: rgba(255, 255, 255, 0.5);
    }
  }

  @media (min-width: 768px) {
    gap: 24px;
    padding: 16px;
  }
}

.message-timestamp {
  text-align: center;
  font-family: 'Work Sans', sans-serif;
  font-size: 12px;
  font-weight: 400;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 8px;
  padding: 4px 0;

  @media (min-width: 768px) {
    font-size: 13px;
    margin-bottom: 10px;
  }
}

.message-group {
  display: flex;
  align-items: flex-start;
  gap: 12px;

  &.short {
    align-items: center;
    height: 44px;
  }

  &.user-group {
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    height: 44px;
  }

  &.gift-group {
    justify-content: flex-end;
    align-items: center;
    gap: 10px;
    margin: 12px 0;
  }

  &.system-group {
    justify-content: center;
    margin: 8px 0;
  }
}

.message-bubble {
  padding: 6px 12px;
  max-width: 60vw;
  word-wrap: break-word;

  @media (min-width: 768px) {
    max-width: 400px;
  }

  &.left {
    align-self: flex-start;
    background: #ffffff;
    border-radius: 0px 12px 12px 12px;
  }

  &.right {
    background: #ca93f2;
    border-radius: 12px 12px 0px 12px;
    max-width: 60vw;
    min-height: 40px;
    display: flex;
    align-items: center;
    gap: 2px;

    @media (min-width: 768px) {
      max-width: 400px;
    }
  }

  &.short {
    height: 40px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .bubble-content {
    font-weight: 400;
    font-size: 14px;
    line-height: 1.4;
    text-align: left;
    max-width: 60vw;
    word-wrap: break-word;
    word-break: break-word;

    @media (min-width: 768px) {
      max-width: 400px;
    }
  }

  &.left .bubble-content {
    color: rgba(0, 0, 0, 0.85);
  }

  &.right .bubble-content {
    color: #ffffff;
  }

  &.short .bubble-content {
    width: auto;
    max-width: none;
  }

  &.right .bubble-content {
    width: auto;
    max-width: none;
    text-align: left;
  }
}

.actor-avatar {
  width: 36px;
  height: 36px;
  border-radius: 50%;
  border: 1px solid #ca93f2;
  overflow: hidden;
  flex-shrink: 0;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 20px;
  border: 1.67px solid #fffcde;
  overflow: hidden;

  img {
    width: 100%;
    height: 100%;
    object-fit: cover;
  }
}

.system-message {
  width: 100%;
  display: flex;
  justify-content: center;

  .system-content {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(8px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    border-radius: 20px;
    padding: 6px 12px;
    display: flex;
    align-items: center;
    max-width: 90%;

    @media (min-width: 768px) {
      max-width: 400px;
      padding: 8px 14px;
    }

    .system-text {
      font-family: 'Work Sans', sans-serif;
      font-size: 12px;
      font-weight: 400;
      color: rgba(255, 255, 255, 0.8);
      text-align: center;
      line-height: 1.3;

      @media (min-width: 768px) {
        font-size: 13px;
      }
    }
  }
}
</style>

/**
 * 火山引擎统计分析工具
 * 简单直接的实现，不依赖shared包
 */

// 类型定义
export interface VolcEventData {
  [key: string]: any
}

// 全局声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}

/**
 * 等待火山引擎主脚本加载完成
 */
function waitForVolcScript(timeout = 5000): Promise<boolean> {
  return new Promise((resolve) => {
    // 检查是否是真正的collectEvent函数（不是缓存队列）
    if (
      window.collectEvent &&
      typeof window.collectEvent === 'function' &&
      !(window.collectEvent as any).q
    ) {
      resolve(true)
      return
    }

    const startTime = Date.now()
    const checkInterval = setInterval(() => {
      // 检查主脚本是否加载完成
      if (
        window.collectEvent &&
        typeof window.collectEvent === 'function' &&
        !(window.collectEvent as any).q
      ) {
        clearInterval(checkInterval)
        resolve(true)
      } else if (Date.now() - startTime > timeout) {
        clearInterval(checkInterval)
        resolve(false)
      }
    }, 100)
  })
}

/**
 * 火山引擎上报函数
 */
export async function trackVolcEvent(
  eventType: string,
  eventData?: VolcEventData,
): Promise<boolean> {
  if (!import.meta.client) {
    return false
  }

  // 等待火山引擎脚本加载
  const isLoaded = await waitForVolcScript()
  if (!isLoaded) {
    console.warn('[Volc Analytics] 火山引擎脚本加载超时')
    return false
  }

  try {
    // 添加通用数据
    const enrichedData = {
      ...eventData,
      timestamp: Date.now(),
      page_url: window.location.href,
      page_path: window.location.pathname,
      user_agent: navigator.userAgent,
    }

    window.collectEvent!(eventType, enrichedData)

    if (process.env.NODE_ENV === 'development') {
      console.log('[Volc Analytics] 事件上报:', eventType, enrichedData)
    }

    return true
  } catch (error) {
    console.error('[Volc Analytics] 事件上报失败:', error)
    return false
  }
}

/**
 * 页面浏览上报
 */
export function trackVolcPageView(path?: string): void {
  if (!import.meta.client) return

  const pagePath = path || window.location.pathname

  trackVolcEvent('PageView', {
    page_path: pagePath,
    page_title: document.title,
    referrer: document.referrer || 'direct',
  })
}

/**
 * 用户行为上报
 */
export function trackVolcUserAction(
  action: string,
  details?: VolcEventData,
): void {
  trackVolcEvent('UserAction', {
    action,
    ...details,
  })
}

/**
 * 页面交互上报
 */
export function trackVolcInteraction(
  element: string,
  action: string,
  details?: VolcEventData,
): void {
  trackVolcEvent('PageInteraction', {
    element,
    action,
    ...details,
  })
}

/**
 * 错误上报
 */
export function trackVolcError(
  errorType: string,
  errorMessage: string,
  details?: VolcEventData,
): void {
  trackVolcEvent('Error', {
    error_type: errorType,
    error_message: errorMessage,
    ...details,
  })
}

/**
 * 检查火山引擎是否可用
 */
export function isVolcAvailable(): boolean {
  return import.meta.client && !!window.collectEvent
}

/**
 * 开发环境调试函数
 */
export function debugVolcAnalytics(): void {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('调试函数只在开发环境可用')
    return
  }

  console.group('🌋 火山引擎调试信息')
  console.log('window.collectEvent 存在:', !!window.collectEvent)
  console.log('是否为缓存队列:', !!(window.collectEvent as any)?.q)
  console.log(
    '主脚本是否加载:',
    window.collectEvent &&
      typeof window.collectEvent === 'function' &&
      !(window.collectEvent as any).q,
  )
  console.log('当前页面:', window.location.href)

  if (window.collectEvent) {
    if ((window.collectEvent as any).q) {
      console.log('⏳ 火山引擎缓存队列已就绪，等待主脚本加载')
      console.log('缓存队列长度:', (window.collectEvent as any).q.length)
    } else {
      console.log('✅ 火山引擎主脚本已加载')
      // 发送测试事件
      trackVolcEvent('debug_test', {
        test: true,
        debug_time: new Date().toISOString(),
      })
      console.log('✅ 测试事件已发送')
    }
  } else {
    console.warn('❌ 火山引擎脚本未加载')
  }

  console.groupEnd()
}

// 开发环境自动添加到全局
if (process.env.NODE_ENV === 'development' && import.meta.client) {
  setTimeout(() => {
    ;(window as any).debugVolcAnalytics = debugVolcAnalytics
    ;(window as any).trackVolcEvent = trackVolcEvent
    console.log('🔧 火山引擎调试工具已加载，使用 debugVolcAnalytics() 进行调试')
  }, 1000)
}

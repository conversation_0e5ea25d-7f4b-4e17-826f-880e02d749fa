import { defineStore } from 'pinia'
import { ref, readonly } from 'vue'
import {
  createPaymentAPI,
  type PriceItem,
  getProjectConfig,
  isPlayshot,
} from 'shared-payment'
import { getDynamicApiHost } from '@/utils/dynamicApiHost'
import { getMainAppPageState } from '@/utils/mainAppState'

export const useRechargeStore = defineStore(
  'recharge',
  () => {
    // 获取支付提供商调试参数（使用 Vite define 注入的变量）
    const debugPaymentProvider =
      (globalThis as any).__VITE_PAYMENT_PROVIDER__ ||
      (import.meta.env.VITE_PAYMENT_PROVIDER as string)

    // 获取 Stripe 公钥（使用 Vite define 注入的变量）
    const stripePublicKey =
      (globalThis as any).__VITE_STRIPE_PUBLIC_KEY__ ||
      (import.meta.env.VITE_STRIPE_PUBLIC_KEY as string)

    console.log(
      '🔧 CSR应用 recharge store - 调试支付提供商:',
      debugPaymentProvider,
    )
    console.log(
      '🔧 CSR应用 recharge store - Stripe公钥:',
      stripePublicKey ? '已配置' : '未配置',
    )

    // 创建支付API实例，传递调试参数和 Stripe 公钥
    const paymentAPI = createPaymentAPI(
      getDynamicApiHost(),
      () => localStorage.getItem('token') || '',
      {
        debugPaymentProvider,
        stripePublicKey,
      },
    )

    // State
    const visible = ref(false)
    const priceList = ref<PriceItem[]>([])
    const loading = ref(false)
    const error = ref<string | null>(null)
    const paymentLoading = ref(false)

    // 获取项目配置
    const projectConfig = getProjectConfig()

    // Actions
    const fetchPriceList = async () => {
      loading.value = true
      error.value = null

      try {
        const data = await paymentAPI.getPriceList()
        priceList.value = data
      } catch (err) {
        error.value =
          err instanceof Error ? err.message : 'Failed to fetch price list'
        throw err
      } finally {
        loading.value = false
      }
    }

    /**
     * 创建支付并跳转
     */
    const createAndRedirectToPayment = async (
      priceId: string,
    ): Promise<void> => {
      // 直接调用 shared-payment，内部已处理所有错误逻辑
      await paymentAPI.createAndRedirectToPayment({
        priceId,
        successUrl: generateSuccessUrl(),
        cancelUrl: generateCancelUrl(),
      })
    }

    const showRechargeModal = () => {
      visible.value = true
    }

    const hideRechargeModal = () => {
      visible.value = false
    }

    const toggleRechargeModal = () => {
      visible.value = !visible.value
    }

    const reset = () => {
      priceList.value = []
      error.value = null
      paymentLoading.value = false
    }

    // 生成成功回调URL - 回到当前页面
    const generateSuccessUrl = (): string => {
      // 优先使用主应用传递的页面状态
      const pageState = getMainAppPageState()
      if (pageState && pageState.currentUrl) {
        console.log(
          '🔗 使用主应用页面状态生成成功回调URL:',
          pageState.currentUrl,
        )
        return pageState.currentUrl
      }

      // 降级方案：使用主应用基础URL
      if (typeof window !== 'undefined') {
        const mainAppUrl =
          import.meta.env.VITE_MAIN_APP_URL || 'http://localhost:3000'
        console.log('🔗 使用降级方案生成成功回调URL:', mainAppUrl)
        return mainAppUrl
      }

      return 'http://localhost:3000'
    }

    // 生成取消回调URL - 回到当前页面
    const generateCancelUrl = (): string => {
      // 优先使用主应用传递的页面状态
      const pageState = getMainAppPageState()
      if (pageState && pageState.currentUrl) {
        console.log(
          '🔗 使用主应用页面状态生成取消回调URL:',
          pageState.currentUrl,
        )
        return pageState.currentUrl
      }

      // 降级方案：使用主应用基础URL
      const mainAppUrl =
        import.meta.env.VITE_MAIN_APP_URL || 'http://localhost:3000'
      console.log('🔗 使用降级方案生成取消回调URL:', mainAppUrl)
      return mainAppUrl
    }

    return {
      // State
      visible,
      priceList,
      loading,
      error,
      paymentLoading,

      // Computed
      projectConfig: readonly(ref(projectConfig)),

      // Actions
      fetchPriceList,
      createAndRedirectToPayment,
      showRechargeModal,
      hideRechargeModal,
      toggleRechargeModal,
      reset,
    }
  },
  {
    persist: false,
  },
)

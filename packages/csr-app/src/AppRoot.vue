<template>
  <a-config-provider :update-at-scroll="true">
    <!-- 在微前端环境中且为 chat 路由时，控制内容显示 -->
    <div v-if="shouldShowContent" class="app-content">
      <!-- Conditionally render either AppPC or AppMobile based on device type and route -->
      <AppPC v-if="shouldShowPCComponent" />
      <AppMobile v-else />
    </div>

    <!-- 微前端加载指示器 -->
    <div
      v-else-if="isInMicroFrontend && isChatRoute && !contentReady"
      class="micro-frontend-loading"
    >
      <div class="loading-spinner"></div>
      <p>Preparing chat...</p>
    </div>

    <!-- 路由性能监控组件 (仅开发环境)
    <RoutePerformanceMonitor /> -->

    <!-- 版本更新通知 -->
    <!-- <UpdateNotification
      v-model:visible="showUpdateNotification"
      :current-version="updateInfo.currentVersion"
      :latest-version="updateInfo.latestVersion"
      :auto-update="true"
      @update="handleUpdate"
      @later="handleUpdateLater"
    /> -->

    <!-- PWA安装提示 -->
    <PWAInstallPrompt />
  </a-config-provider>
</template>

<script setup lang="ts">
import { computed, onMounted, ref, onUnmounted, watch } from 'vue'
import { useRoute } from 'vue-router'
import { useStateSync } from '@/composables/useStateSync'
import AppPC from './AppPC.vue'
import AppMobile from './AppMobile.vue'
import PWAInstallPrompt from '@/components/PWAInstallPrompt.vue'
import RoutePerformanceMonitor from '@/components/RoutePerformanceMonitor.vue'
import UpdateNotification from '@/shared/components/UpdateNotification.vue'
import { useRouteAdaptation } from '@/composables/useRouteAdaptation'
import { useReferralStore } from '@/store/referral'
import { useChatEventsStore } from '@/store/chat-events'
import { reportEvent } from './utils'
import { ReportEvent } from './interface'
import { useDeviceDetection } from '@/composables/useDeviceDetection'
import { useThemeStore } from '@/store/theme'
import { useSysConfigStore } from '@/store/sysconfig'
import { useUserStore } from '@/store/user'
import { useChatStore } from '@/store/chat'
import { sendUserLoginStatusEvent } from '@/utils/googleAnalytics'

const route = useRoute()
const referralStore = useReferralStore()
const themeStore = useThemeStore()
const sysConfigStore = useSysConfigStore()
const userStore = useUserStore()
const chatStore = useChatStore()
const chatEventsStore = useChatEventsStore()

// 使用状态同步
const { isInMicroFrontend } = useStateSync()

const { isDesktop } = useDeviceDetection()

// 微前端内容显示控制
const contentReady = ref(false)

// 检查是否是 chat 路由
const isChatRoute = computed(() => {
  const path = route.path
  return (
    path.startsWith('/chat') ||
    path.startsWith('/chat2') ||
    path.startsWith('/chat3') ||
    path.startsWith('/chat4')
  )
})

// 决定是否显示内容
const shouldShowContent = computed(() => {
  // 如果不在微前端环境中，直接显示
  if (!isInMicroFrontend) {
    return true
  }

  // 如果在微前端环境中但不是 chat 路由，直接显示
  if (!isChatRoute.value) {
    return true
  }

  // 如果是微前端环境中的 chat 路由，等待内容准备就绪
  return contentReady.value
})

// 版本更新相关状态
const showUpdateNotification = ref(false)
const updateInfo = ref({
  currentVersion: '',
  latestVersion: '',
  onUpdate: null as (() => void) | null,
  onLater: null as (() => void) | null,
})

// 检查当前路由是否是PC路由
const isPCRoute = computed(() => {
  return route.path.startsWith('/pc')
})

// 根据设备类型和路由决定是否显示PC组件
const shouldShowPCComponent = computed(() => {
  // 如果是PC路由，始终使用PC组件
  if (isPCRoute.value) return true

  // 如果是桌面设备，使用PC组件
  if (isDesktop.value) return true

  // 其他情况使用移动端组件
  return false
})

// 版本更新处理函数
const handleVersionUpdateEvent = (event: Event) => {
  const customEvent = event as CustomEvent
  const { newVersion, currentVersion, onUpdate, onLater } = customEvent.detail

  updateInfo.value = {
    currentVersion: currentVersion.version,
    latestVersion: newVersion.version,
    onUpdate,
    onLater,
  }

  showUpdateNotification.value = true
}

// 立即检查URL参数（在setup阶段，确保在子组件初始化前执行）
if (typeof window !== 'undefined') {
  const urlParams = new URLSearchParams(window.location.search)

  // Check for source_code in URL parameters
  const sourceCode = urlParams.get('source')
  if (sourceCode) {
    referralStore.setSourceCode(sourceCode)
  }

  // Check for invite_code in URL parameters
  const inviteCode = urlParams.get('invite')
  if (inviteCode) {
    referralStore.setInviteCode(inviteCode)
  }

  // 不再通过URL参数检查restart状态，改用postMessage方式

  reportEvent(ReportEvent.ReelPlayFirstVisit, {
    sourceCode: sourceCode || '',
    inviteCode: inviteCode || '',
  })
}

// 监听路由变化，在 chat 路由完成后标记内容准备就绪
watch(
  route,
  async (newRoute) => {
    if (isInMicroFrontend && isChatRoute.value) {
      console.log('🔄 微前端环境中的 chat 路由变化:', newRoute.path)

      // 延迟一点时间确保组件完全渲染
      setTimeout(async () => {
        contentReady.value = true
        console.log('✅ 微前端 chat 内容准备就绪')

        // 发送内容准备就绪消息
        try {
          const { sendMessageToParent } = await import(
            '@/utils/iframeNavigation'
          )
          sendMessageToParent('CHAT_ROUTE_READY', {
            path: newRoute.path,
            timestamp: Date.now(),
            source: 'app-root',
          })
          console.log('📤 AppRoot: 已发送 CHAT_ROUTE_READY 消息')
        } catch (error) {
          console.warn('发送路由就绪消息失败:', error)
          // 即使消息发送失败，也要确保内容显示
          contentReady.value = true
        }
      }, 200)

      // 降级机制：如果 200ms 后还没有标记为就绪，强制标记
      setTimeout(() => {
        if (!contentReady.value) {
          console.warn('⚠️ 微前端内容准备超时，强制标记为就绪')
          contentReady.value = true
        }
      }, 1000)
    }
  },
  { immediate: true },
)

// Detect and store source_code from URL parameters
onMounted(() => {
  // 初始化主题 - 确保在DOM加载后立即执行
  setTimeout(() => {
    themeStore.initTheme()
    // themeStore.listenForSystemThemeChanges()
  }, 0)
  sysConfigStore.fetchConfigs()

  // 监听版本更新事件
  window.addEventListener('version-update-available', handleVersionUpdateEvent)

  // 检查是否有source_code用于事件收集
  if (referralStore.getSourceCode() && window?.collectEvent) {
    window.collectEvent('config', {
      kol_source: referralStore.getSourceCode(),
    })
  }

  // 如果不在微前端环境或不是 chat 路由，立即标记内容准备就绪
  if (!isInMicroFrontend || !isChatRoute.value) {
    contentReady.value = true
  }
})

onUnmounted(() => {
  // 清理事件监听器
  window.removeEventListener(
    'version-update-available',
    handleVersionUpdateEvent,
  )
})

let lastReportedLoginState = ref(false)

watch(
  () => userStore.userInfo?.role,
  (newVal) => {
    if (!newVal) return
    const isLoggedIn = newVal === 'normal' || newVal === 'admin'

    // 避免重复上报
    if (lastReportedLoginState.value === isLoggedIn) return
    lastReportedLoginState.value = isLoggedIn

    // 发送用户登录状态事件
    sendUserLoginStatusEvent(isLoggedIn)
  },
  { immediate: false },
)

// Route adaptation
useRouteAdaptation()
</script>

<style lang="less">
#app {
  position: relative;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  font-family: var(--font-family);
}

.app-content {
  width: 100%;
  height: 100%;
}

.micro-frontend-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  z-index: 9999;
}

.micro-frontend-loading .loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e0e0e0;
  border-top: 3px solid #667eea;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

.micro-frontend-loading p {
  color: #666;
  font-size: 14px;
  margin: 0;
}
</style>

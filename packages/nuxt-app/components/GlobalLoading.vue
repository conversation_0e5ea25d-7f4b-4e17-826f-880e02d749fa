<template>
  <Teleport to="body">
    <Transition name="loading-fade">
      <div v-if="loadingStore.isLoading" class="global-loading-overlay">
        <div class="loading-content">
          <!-- Loading Icon -->
          <div class="loading-icon-wrapper">
            <Icon 
              :name="getLoadingIcon" 
              :size="isMobile ? 48 : 64" 
              class="loading-icon" 
            />
          </div>
          
          <!-- Loading Message -->
          <p class="loading-message">{{ loadingStore.message }}</p>
          
          <!-- Progress Indicator for Payment -->
          <div v-if="loadingStore.type === 'payment'" class="progress-steps">
            <div class="step" :class="{ active: currentStep >= 1 }">
              <Icon name="material-symbols:credit-card" size="20" />
              <span>Processing</span>
            </div>
            <div class="step" :class="{ active: currentStep >= 2 }">
              <Icon name="material-symbols:security" size="20" />
              <span>Verifying</span>
            </div>
            <div class="step" :class="{ active: currentStep >= 3 }">
              <Icon name="material-symbols:check-circle" size="20" />
              <span>Complete</span>
            </div>
          </div>
        </div>
      </div>
    </Transition>
  </Teleport>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'

// Composables
const loadingStore = useLoadingStore()
const { isMobile } = useDeviceDetection()

// State
const currentStep = ref(1)
let stepInterval: NodeJS.Timeout | null = null

// Computed
const getLoadingIcon = computed(() => {
  switch (loadingStore.type) {
    case 'payment':
      return 'material-symbols:credit-card'
    case 'redirect':
      return 'material-symbols:open-in-new'
    default:
      return 'material-symbols:sync'
  }
})

// Watch for payment loading to animate steps
watch(() => loadingStore.type, (newType) => {
  if (newType === 'payment') {
    currentStep.value = 1
    stepInterval = setInterval(() => {
      if (currentStep.value < 3) {
        currentStep.value++
      }
    }, 1500)
  } else {
    if (stepInterval) {
      clearInterval(stepInterval)
      stepInterval = null
    }
    currentStep.value = 1
  }
})

watch(() => loadingStore.isLoading, (isLoading) => {
  if (!isLoading && stepInterval) {
    clearInterval(stepInterval)
    stepInterval = null
    currentStep.value = 1
  }
})

// Handle page visibility changes
const handleVisibilityChange = () => {
  if (document.visibilityState === 'visible') {
    // 页面变为可见时，如果loading还在显示，清除它
    // 这种情况通常发生在从外部页面回退时
    if (loadingStore.isLoading) {
      console.log('Page became visible with loading active, clearing loading state')
      loadingStore.hide()
    }
  }
}

// Handle browser back/forward navigation
const handlePageShow = (e: PageTransitionEvent) => {
  // 当页面从缓存中恢复时清除loading状态
  if (e.persisted && loadingStore.isLoading) {
    console.log('Page restored from cache with loading active, clearing loading state')
    loadingStore.hide()
  }
}

onMounted(() => {
  // 监听页面可见性变化
  document.addEventListener('visibilitychange', handleVisibilityChange)
  // 监听页面显示事件（包括从缓存恢复）
  window.addEventListener('pageshow', handlePageShow)
})

// Cleanup
onUnmounted(() => {
  if (stepInterval) {
    clearInterval(stepInterval)
  }
  document.removeEventListener('visibilitychange', handleVisibilityChange)
  window.removeEventListener('pageshow', handlePageShow)
})
</script>

<style lang="less" scoped>
.global-loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  
  .loading-content {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    border-radius: 20px;
    padding: 40px;
    text-align: center;
    max-width: 300px;
    width: 90%;
    box-shadow: 0 20px 40px var(--shadow-color);
    
    .loading-icon-wrapper {
      margin-bottom: 20px;
      
      .loading-icon {
        color: var(--accent-color);
        animation: spin 1s linear infinite;
      }
    }
    
    .loading-message {
      font-size: 16px;
      color: var(--text-primary);
      font-weight: 500;
      margin-bottom: 24px;
      line-height: 1.4;
    }
    
    .progress-steps {
      display: flex;
      justify-content: space-between;
      gap: 12px;
      
      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        gap: 8px;
        flex: 1;
        opacity: 0.4;
        transition: all 0.3s ease;
        font-size: 12px;
        color: var(--text-secondary);
        
        &.active {
          opacity: 1;
          color: var(--accent-color);
          transform: scale(1.05);
        }
        
        span {
          font-weight: 500;
        }
      }
    }
  }
}

// Mobile optimizations
@media (max-width: 768px) {
  .global-loading-overlay {
    .loading-content {
      padding: 30px 20px;
      
      .loading-message {
        font-size: 14px;
        margin-bottom: 20px;
      }
      
      .progress-steps {
        gap: 8px;
        
        .step {
          font-size: 11px;
          gap: 6px;
        }
      }
    }
  }
}

// Animations
@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

// Transition animations
.loading-fade-enter-active,
.loading-fade-leave-active {
  transition: all 0.3s ease;
}

.loading-fade-enter-from {
  opacity: 0;
  
  .loading-content {
    transform: scale(0.8) translateY(20px);
  }
}

.loading-fade-leave-to {
  opacity: 0;
  
  .loading-content {
    transform: scale(0.8) translateY(-20px);
  }
}
</style>
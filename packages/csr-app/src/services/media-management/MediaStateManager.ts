/**
 * 媒体状态管理器 - 统一管理所有媒体播放状态和事件处理
 * 解决原有架构中的事件冲突和状态混乱问题
 */

import { ref, reactive, computed, type Ref } from 'vue'
import type {
  MediaManagerState,
  MediaManagerConfig,
  MediaManagerCallbacks,
  MediaEvent,
  EventHandleResult,
  MediaResource,
  MediaType,
  MediaState,
  EventPriority,
} from './types'
import { DEFAULT_MEDIA_CONFIG } from './types'

export class MediaStateManager {
  // 状态管理
  private state: Ref<MediaManagerState>
  private config: MediaManagerConfig
  private callbacks: MediaManagerCallbacks

  // 事件队列
  private eventQueue: Ref<MediaEvent[]> = ref([])
  private pendingEvents: Ref<MediaEvent[]> = ref([]) // 延迟处理的事件队列
  private isProcessingEvents = false

  // 资源缓存
  private resourceCache = new Map<string, MediaResource>()
  private preloadPromises = new Map<string, Promise<void>>()

  constructor(
    config: Partial<MediaManagerConfig> = {},
    callbacks: MediaManagerCallbacks = {},
  ) {
    this.config = { ...DEFAULT_MEDIA_CONFIG, ...config }
    this.callbacks = callbacks

    // 初始化状态
    this.state = ref(this.createInitialState())

    // 开始事件处理循环
    this.startEventProcessing()

    if (this.config.debugMode) {
      console.log('🎬 MediaStateManager initialized', this.config)
    }
  }

  /**
   * 创建初始状态
   */
  private createInitialState(): MediaManagerState {
    return {
      liveBackground: {
        state: MediaState.IDLE,
        config: null,
        currentResource: null,
      },
      foreground: {
        state: MediaState.IDLE,
        config: null,
        currentResource: null,
      },
      audio: {
        tts: {
          isPlaying: false,
          currentMessage: undefined,
          queue: [],
        },
        bgm: {
          isPlaying: false,
          volume: 1.0,
          originalVolume: 1.0,
        },
        video: {
          hasAudio: false,
          isMuted: true,
        },
      },
      global: {
        isUserInteracting: false,
        isPaused: false,
        lastUserAction: 0,
      },
    }
  }

  /**
   * 获取当前状态（只读）
   */
  get currentState(): MediaManagerState {
    return { ...this.state.value }
  }

  /**
   * 获取状态的响应式引用
   */
  get stateRef(): Ref<MediaManagerState> {
    return this.state
  }

  /**
   * 计算属性：是否有活跃的前景媒体
   */
  get hasActiveForeground(): boolean {
    const foregroundState = this.state.value.foreground.state
    return (
      foregroundState === MediaState.PLAYING ||
      foregroundState === MediaState.LOADING
    )
  }

  /**
   * 计算属性：是否可以播放直播背景
   */
  get canPlayLiveBackground(): boolean {
    return !this.hasActiveForeground && !this.state.value.global.isPaused
  }

  /**
   * 检查当前直播背景视频是否可以被打断
   */
  get isCurrentVideoInterruptible(): boolean {
    const currentResource = this.state.value.liveBackground.currentResource
    if (!currentResource) return true

    // 如果视频标记为not_loop=true，则不可打断
    return !currentResource.not_loop
  }

  /**
   * 添加事件到队列
   */
  addEvent(event: Omit<MediaEvent, 'id' | 'timestamp'>): string {
    const fullEvent: MediaEvent = {
      ...event,
      id: `event_${Date.now()}_${Math.random().toString(36).substring(2)}`,
      timestamp: Date.now(),
    }

    // 检查是否需要延迟处理
    const shouldDelay = this.shouldDelayEvent(fullEvent)

    if (shouldDelay) {
      // 添加到延迟队列
      this.addToPendingQueue(fullEvent)
      if (this.config.debugMode) {
        console.log(
          '⏳ Event delayed due to non-interruptible video:',
          fullEvent.type,
        )
      }
    } else {
      // 正常添加到事件队列
      this.addToEventQueue(fullEvent)
    }

    return fullEvent.id
  }

  /**
   * 判断事件是否需要延迟处理
   */
  private shouldDelayEvent(event: MediaEvent): boolean {
    // 只有前景媒体事件需要考虑延迟
    const isForegroundEvent =
      event.type.includes('foreground') ||
      event.type === 'play_video' ||
      event.type === 'play_image'

    if (!isForegroundEvent) return false

    // 检查当前视频是否可以被打断
    return !this.isCurrentVideoInterruptible
  }

  /**
   * 添加到正常事件队列
   */
  private addToEventQueue(event: MediaEvent): void {
    // 按优先级插入队列
    const insertIndex = this.eventQueue.value.findIndex(
      (e) => e.priority > event.priority,
    )

    if (insertIndex === -1) {
      this.eventQueue.value.push(event)
    } else {
      this.eventQueue.value.splice(insertIndex, 0, event)
    }

    if (this.config.debugMode) {
      console.log(
        '📝 Event added to queue:',
        event.type,
        'Priority:',
        event.priority,
      )
    }
  }

  /**
   * 添加到延迟队列
   */
  private addToPendingQueue(event: MediaEvent): void {
    // 按优先级插入延迟队列
    const insertIndex = this.pendingEvents.value.findIndex(
      (e) => e.priority > event.priority,
    )

    if (insertIndex === -1) {
      this.pendingEvents.value.push(event)
    } else {
      this.pendingEvents.value.splice(insertIndex, 0, event)
    }

    if (this.config.debugMode) {
      console.log(
        '⏳ Event added to pending queue:',
        event.type,
        'Priority:',
        event.priority,
      )
    }
  }

  /**
   * 移除事件
   */
  removeEvent(eventId: string): boolean {
    const index = this.eventQueue.value.findIndex((e) => e.id === eventId)
    if (index !== -1) {
      const event = this.eventQueue.value[index]
      if (event.abortController) {
        event.abortController.abort()
      }
      this.eventQueue.value.splice(index, 1)
      return true
    }
    return false
  }

  /**
   * 清空指定优先级以下的事件
   */
  clearLowerPriorityEvents(priority: EventPriority): void {
    const eventsToRemove = this.eventQueue.value.filter(
      (e) => e.priority > priority,
    )
    eventsToRemove.forEach((event) => {
      if (event.abortController) {
        event.abortController.abort()
      }
    })
    this.eventQueue.value = this.eventQueue.value.filter(
      (e) => e.priority <= priority,
    )

    if (this.config.debugMode && eventsToRemove.length > 0) {
      console.log(`🗑️ Cleared ${eventsToRemove.length} lower priority events`)
    }
  }

  /**
   * 处理延迟队列中的事件（当不可打断的视频播放完毕时调用）
   */
  processPendingEvents(): void {
    if (this.pendingEvents.value.length === 0) return

    if (this.config.debugMode) {
      console.log(
        `🚀 Processing ${this.pendingEvents.value.length} pending events`,
      )
    }

    // 将延迟队列中的事件移动到正常队列
    const eventsToProcess = [...this.pendingEvents.value]
    this.pendingEvents.value = []

    // 按优先级添加到事件队列
    eventsToProcess.forEach((event) => {
      this.addToEventQueue(event)
    })
  }

  /**
   * 开始事件处理循环
   */
  private async startEventProcessing(): Promise<void> {
    if (this.isProcessingEvents) return

    this.isProcessingEvents = true

    while (this.isProcessingEvents) {
      if (this.eventQueue.value.length > 0) {
        const event = this.eventQueue.value.shift()!
        await this.processEvent(event)
      } else {
        // 没有事件时短暂等待
        await new Promise((resolve) => setTimeout(resolve, 16)) // ~60fps
      }
    }
  }

  /**
   * 处理单个事件
   */
  private async processEvent(event: MediaEvent): Promise<void> {
    try {
      if (this.config.debugMode) {
        console.log('⚡ Processing event:', event.type, event.mediaType)
      }

      let result: EventHandleResult

      // 根据媒体类型分发事件
      switch (event.mediaType) {
        case MediaType.LIVE_BACKGROUND:
          result = await this.handleLiveBackgroundEvent(event)
          break
        case MediaType.FOREGROUND_VIDEO:
        case MediaType.FOREGROUND_IMAGE:
          result = await this.handleForegroundEvent(event)
          break
        case MediaType.AUDIO_TTS:
        case MediaType.AUDIO_BGM:
          result = await this.handleAudioEvent(event)
          break
        default:
          result = {
            success: false,
            message: `Unknown media type: ${event.mediaType}`,
          }
      }

      // 触发回调
      if (this.callbacks.onEventProcessed) {
        this.callbacks.onEventProcessed(event, result)
      }

      // 如果有下一个事件，添加到队列
      if (result.nextEvent) {
        this.addEvent(result.nextEvent)
      }
    } catch (error) {
      console.error('❌ Error processing event:', error)
      if (this.callbacks.onError) {
        this.callbacks.onError(
          error as Error,
          `Processing event: ${event.type}`,
        )
      }
    }
  }

  /**
   * 处理直播背景事件
   */
  private async handleLiveBackgroundEvent(
    event: MediaEvent,
  ): Promise<EventHandleResult> {
    // 如果有前景媒体在播放，延迟处理
    if (this.hasActiveForeground) {
      // 重新加入队列，稍后处理
      setTimeout(() => {
        this.addEvent({
          type: event.type,
          priority: event.priority,
          mediaType: event.mediaType,
          data: event.data,
        })
      }, 1000)

      return {
        success: true,
        message: 'Deferred due to active foreground media',
      }
    }

    // 具体的直播背景处理逻辑将在 LiveStreamBackgroundManager 中实现
    return { success: true }
  }

  /**
   * 处理前景媒体事件
   */
  private async handleForegroundEvent(
    event: MediaEvent,
  ): Promise<EventHandleResult> {
    // 前景媒体有最高优先级，暂停直播背景
    if (this.state.value.liveBackground.state === MediaState.PLAYING) {
      this.updateState('liveBackground', { state: MediaState.PAUSED })
    }

    // 具体的前景媒体处理逻辑将在 ForegroundMediaPlayer 中实现
    return { success: true }
  }

  /**
   * 处理音频事件
   */
  private async handleAudioEvent(
    event: MediaEvent,
  ): Promise<EventHandleResult> {
    // 具体的音频处理逻辑将在 AudioStateManager 中实现
    return { success: true }
  }

  /**
   * 更新状态
   */
  private updateState(section: keyof MediaManagerState, updates: any): void {
    this.state.value = {
      ...this.state.value,
      [section]: {
        ...this.state.value[section],
        ...updates,
      },
    }

    // 触发状态变化回调
    if (this.callbacks.onStateChange) {
      this.callbacks.onStateChange(this.state.value)
    }
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.isProcessingEvents = false

    // 取消所有待处理的事件
    this.eventQueue.value.forEach((event) => {
      if (event.abortController) {
        event.abortController.abort()
      }
    })
    this.eventQueue.value = []

    // 清空缓存
    this.resourceCache.clear()
    this.preloadPromises.clear()

    if (this.config.debugMode) {
      console.log('🔥 MediaStateManager destroyed')
    }
  }
}

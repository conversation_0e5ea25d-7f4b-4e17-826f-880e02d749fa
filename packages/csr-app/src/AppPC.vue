<template>
  <a-config-provider :update-at-scroll="true">
    <template v-if="isPCRoute">
      <!-- 原有PC端路由 -->
      <RouterView />
    </template>
    <template v-else>
      <!-- 适配移动端路由到PC端组件 -->
      <div class="pc-adaptive-container">
        <AdaptiveComponentLoader
          v-if="currentRoutePath"
          :path="currentRoutePath"
        />
        <RouterView v-else />
      </div>
    </template>
  </a-config-provider>
</template>

<script lang="ts" setup>
import { computed, onMounted } from 'vue'
import { useRoute } from 'vue-router'
import AdaptiveComponentLoader from '@/pc/components/AdaptiveComponentLoader.vue'
import { useDeviceDetection } from '@/composables/useDeviceDetection'
import { shouldUsePCLayout } from '@/pc/routes/pc-route-mapping'

const route = useRoute()
const { isDesktop } = useDeviceDetection()

// 检查当前路由是否是PC路由
const isPCRoute = computed(() => {
  return route.path.startsWith('/pc')
})

// 获取当前路由对应的PC端组件路径
const currentRoutePath = computed(() => {
  // 如果是PC路由，返回null，使用RouterView
  if (isPCRoute.value) return null

  // 使用集中的路由映射配置判断是否使用PCLayout
  return shouldUsePCLayout(route.path) ? 'PCLayout' : null
})

onMounted(() => {
  // 添加PC端标识类
  if (isDesktop.value) {
    document.body.classList.add('pc-mode')
  }

  // 检查是否在微前端环境中
  const isInIframe = typeof window !== 'undefined' && window.self !== window.top
  const isChatRoute = route.path.startsWith('/chat')

  if (isInIframe && isChatRoute) {
    // 在微前端的 chat 环境中，延迟显示以避免闪现
    console.log('🔄 微前端 chat 环境，延迟显示 PC Layout')

    const showPCLayout = () => {
      const layoutElement = document.querySelector(
        '.pc-adaptive-container',
      ) as HTMLElement
      if (layoutElement) {
        layoutElement.style.opacity = '1'
        layoutElement.style.visibility = 'visible'
        console.log('✅ PC Layout 已显示')
      }
    }

    // 初始隐藏
    const layoutElement = document.querySelector(
      '.pc-adaptive-container',
    ) as HTMLElement
    if (layoutElement) {
      layoutElement.style.opacity = '0'
      layoutElement.style.visibility = 'hidden'
    }

    // 延迟显示
    setTimeout(showPCLayout, 300)

    // 备用方案
    setTimeout(showPCLayout, 800)
  }
})
</script>

<style lang="less">
#app {
  font-family: var(--font-family);
}

.pc-adaptive-container {
  width: 100%;
  min-height: calc(var(--vh, 1vh) * 100);
  background-color: #180430;
  transition:
    opacity 0.3s ease-in-out,
    visibility 0.3s ease-in-out;
}

// PC端全局样式
body.pc-mode {
  background-color: #180430;
  margin: 0;
  padding: 0;

  #app {
    max-width: 100% !important;
    height: auto !important;
    border-radius: 0 !important;
    box-shadow: none !important;
    transform: none !important;

    &::before {
      display: none !important;
    }
  }
}
</style>

<template>
  <div class="chat4-loading" :class="{ visible: visible }">
    <!-- 背景遮罩 -->
    <div class="loading-backdrop"></div>

    <!-- 主要加载内容 -->
    <div class="loading-content">
      <!-- 头像容器 -->
      <div class="avatar-container">
        <div class="avatar-ring">
          <div class="avatar-ring-inner"></div>
          <img
            v-if="characterAvatar"
            :src="characterAvatar"
            :alt="characterName"
            class="character-avatar"
          />
          <div v-else class="avatar-placeholder">
            <div class="placeholder-content">
              <div class="avatar-silhouette"></div>
              <!-- <div class="loading-shimmer"></div> -->
            </div>
          </div>
        </div>

        <!-- 脉冲效果 -->
        <div class="pulse-ring pulse-ring-1"></div>
        <div class="pulse-ring pulse-ring-2"></div>
        <div class="pulse-ring pulse-ring-3"></div>
      </div>

      <!-- 加载文本 -->
      <div class="loading-text">
        <h3 class="loading-title">{{ loadingTitle }}</h3>
        <p class="loading-subtitle">{{ loadingSubtitle }}</p>
      </div>

      <!-- 进度指示器 -->
      <div class="progress-container">
        <div class="progress-bar">
          <div class="progress-fill" :style="{ width: `${progress}%` }"></div>
        </div>
        <div class="progress-dots">
          <div
            v-for="i in 3"
            :key="i"
            class="progress-dot"
            :class="{ active: progress / 33.33 >= i }"
          ></div>
        </div>
      </div>
    </div>

    <!-- 装饰性粒子 -->
    <div class="particles">
      <!-- 主要粒子轨道 -->
      <div
        v-for="i in 8"
        :key="`main-${i}`"
        class="particle main-particle"
        :style="{
          '--delay': `${i * 0.3}s`,
          '--angle': `${i * 45}deg`,
          '--radius': '140px',
          '--duration': '6s',
        }"
      ></div>

      <!-- 次要粒子轨道 -->
      <div
        v-for="i in 6"
        :key="`secondary-${i}`"
        class="particle secondary-particle"
        :style="{
          '--delay': `${i * 0.4}s`,
          '--angle': `${i * 60 + 30}deg`,
          '--radius': '100px',
          '--duration': '8s',
        }"
      ></div>

      <!-- 装饰小粒子 -->
      <div
        v-for="i in 12"
        :key="`tiny-${i}`"
        class="particle tiny-particle"
        :style="{
          '--delay': `${i * 0.15}s`,
          '--angle': `${i * 30}deg`,
          '--radius': '180px',
          '--duration': '10s',
        }"
      ></div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, onMounted } from 'vue'

interface Props {
  visible?: boolean
  characterAvatar?: string
  characterName?: string
  loadingType?:
    | 'scene-transition'
    | 'message-loading'
    | 'connection'
    | 'general'
  customTitle?: string
  customSubtitle?: string
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  characterName: 'Character',
  loadingType: 'general',
})

const emit = defineEmits<{
  complete: []
}>()

// 进度状态
const progress = ref(0)
const progressInterval = ref<ReturnType<typeof setInterval> | null>(null)

// 根据加载类型计算文本
const loadingTitle = computed(() => {
  if (props.customTitle) return props.customTitle

  switch (props.loadingType) {
    case 'scene-transition':
      return 'Switching Scene'
    case 'message-loading':
      return 'Loading Messages'
    case 'connection':
      return 'Connecting'
    default:
      return 'Loading'
  }
})

const loadingSubtitle = computed(() => {
  if (props.customSubtitle) return props.customSubtitle

  switch (props.loadingType) {
    case 'scene-transition':
      return `Preparing ${props.characterName}'s space...`
    case 'message-loading':
      return 'Fetching conversation history...'
    case 'connection':
      return `Connecting to ${props.characterName}...`
    default:
      return 'Please wait a moment...'
  }
})

// 模拟进度更新
const startProgress = () => {
  progress.value = 0

  if (progressInterval.value) {
    clearInterval(progressInterval.value)
  }

  progressInterval.value = setInterval(() => {
    if (progress.value < 90) {
      // 前90%快速增长
      progress.value += Math.random() * 15 + 5
    } else if (progress.value < 95) {
      // 90-95%慢速增长
      progress.value += Math.random() * 2 + 1
    }
    // 95%以上等待外部完成
  }, 200)
}

const completeProgress = () => {
  if (progressInterval.value) {
    clearInterval(progressInterval.value)
    progressInterval.value = null
  }

  progress.value = 100

  // 完成动画后触发事件
  setTimeout(() => {
    emit('complete')
  }, 500)
}

// 监听可见性变化
watch(
  () => props.visible,
  (newVisible) => {
    if (newVisible) {
      startProgress()
    } else {
      if (progressInterval.value) {
        clearInterval(progressInterval.value)
        progressInterval.value = null
      }
      progress.value = 0
    }
  },
)

// 暴露完成方法给父组件
defineExpose({
  completeProgress,
})

onMounted(() => {
  if (props.visible) {
    startProgress()
  }
})
</script>

<style lang="less" scoped>
.chat4-loading {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: calc(var(--vh, 1vh) * 100);
  z-index: 9500; /* 高于RemoteChatLoader(8888)，确保场景切换loading能正常显示 */
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.visible {
    opacity: 1;
    visibility: visible;
  }
}

.loading-backdrop {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    135deg,
    rgba(31, 0, 56, 0.95) 0%,
    rgba(76, 60, 89, 0.95) 50%,
    rgba(124, 77, 255, 0.95) 100%
  );
  backdrop-filter: blur(20px);
}

.loading-content {
  position: relative;
  display: flex;
  flex-direction: column;
  align-items: center;
  z-index: 2;
  padding: 40px 20px;
}

/* 头像容器 */
.avatar-container {
  position: relative;
  margin-bottom: 32px;
}

.avatar-ring {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(45deg, #7c4dff, #ff4081, #00bcd4);
  padding: 4px;
  animation: rotate 3s linear infinite;

  .avatar-ring-inner {
    width: 100%;
    height: 100%;
    border-radius: 50%;
    background: #1f0038;
    padding: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
  }

  .character-avatar {
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    border-radius: 50%;
    object-fit: cover;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
  }

  .avatar-placeholder {
    width: calc(100% - 16px);
    height: calc(100% - 16px);
    border-radius: 50%;
    background: linear-gradient(
      135deg,
      rgba(124, 77, 255, 0.2),
      rgba(255, 64, 129, 0.2)
    );
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.1);
    overflow: hidden;

    .placeholder-content {
      position: relative;
      width: 60%;
      height: 60%;
      display: flex;
      align-items: center;
      justify-content: center;
    }

    .avatar-silhouette {
      width: 100%;
      height: 100%;
      background: rgba(255, 255, 255, 0.1);
      border-radius: 50%;
      position: relative;

      &::before {
        content: '';
        position: absolute;
        top: 30%;
        left: 50%;
        transform: translateX(-50%);
        width: 40%;
        height: 40%;
        background: rgba(255, 255, 255, 0.2);
        border-radius: 50%;
      }
    }

    .loading-shimmer {
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(
        90deg,
        transparent,
        rgba(255, 255, 255, 0.2),
        transparent
      );
      animation: shimmer-avatar 2s ease-in-out infinite;
    }
  }
}

.pulse-ring {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  border: 2px solid rgba(124, 77, 255, 0.3);
  border-radius: 50%;
  animation: pulse 2s ease-out infinite;

  &.pulse-ring-1 {
    width: 140px;
    height: 140px;
    animation-delay: 0s;
  }

  &.pulse-ring-2 {
    width: 160px;
    height: 160px;
    animation-delay: 0.7s;
  }

  &.pulse-ring-3 {
    width: 180px;
    height: 180px;
    animation-delay: 1.4s;
  }
}

/* 加载文本 */
.loading-text {
  text-align: center;
  margin-bottom: 32px;

  .loading-title {
    font-family: 'Work Sans', sans-serif;
    font-size: 24px;
    font-weight: 600;
    color: #ffffff;
    margin: 0 0 8px 0;
    letter-spacing: 0.5px;
  }

  .loading-subtitle {
    font-family: 'Work Sans', sans-serif;
    font-size: 14px;
    font-weight: 400;
    color: rgba(255, 255, 255, 0.7);
    margin: 0;
    line-height: 1.4;
  }
}

/* 进度指示器 */
.progress-container {
  width: 240px;

  .progress-bar {
    width: 100%;
    height: 4px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
    overflow: hidden;
    margin-bottom: 16px;

    .progress-fill {
      height: 100%;
      background: linear-gradient(90deg, #7c4dff, #ff4081);
      border-radius: 2px;
      transition: width 0.3s ease;
      position: relative;

      &::after {
        content: '';
        position: absolute;
        top: 0;
        right: 0;
        width: 20px;
        height: 100%;
        background: linear-gradient(
          90deg,
          transparent,
          rgba(255, 255, 255, 0.3)
        );
        animation: shimmer 1.5s ease-in-out infinite;
      }
    }
  }

  .progress-dots {
    display: flex;
    justify-content: center;
    gap: 12px;

    .progress-dot {
      width: 8px;
      height: 8px;
      border-radius: 50%;
      background: rgba(255, 255, 255, 0.3);
      transition: all 0.3s ease;

      &.active {
        background: #7c4dff;
        box-shadow: 0 0 12px rgba(124, 77, 255, 0.6);
        transform: scale(1.2);
      }
    }
  }
}

/* 装饰性粒子 */
.particles {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 400px;
  height: 400px;
  pointer-events: none;

  .particle {
    position: absolute;
    top: 50%;
    left: 50%;
    border-radius: 50%;
    transform-origin: 0 0;
    animation: orbit var(--duration) linear infinite;
    animation-delay: var(--delay);
    transform: translate(-50%, -50%) rotate(var(--angle))
      translateY(calc(-1 * var(--radius))) rotate(calc(-1 * var(--angle)));
  }

  .main-particle {
    width: 6px;
    height: 6px;
    background: radial-gradient(
      circle,
      rgba(124, 77, 255, 0.8),
      rgba(124, 77, 255, 0.3)
    );
    box-shadow:
      0 0 10px rgba(124, 77, 255, 0.5),
      0 0 20px rgba(124, 77, 255, 0.2);

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      width: 10px;
      height: 10px;
      background: radial-gradient(
        circle,
        rgba(255, 64, 129, 0.6),
        transparent 70%
      );
      border-radius: 50%;
      animation: pulse-glow 3s ease-in-out infinite alternate;
    }
  }

  .secondary-particle {
    width: 4px;
    height: 4px;
    background: radial-gradient(
      circle,
      rgba(255, 64, 129, 0.7),
      rgba(255, 64, 129, 0.2)
    );
    box-shadow:
      0 0 8px rgba(255, 64, 129, 0.4),
      0 0 16px rgba(255, 64, 129, 0.1);

    &::after {
      content: '';
      position: absolute;
      top: -1px;
      left: -1px;
      width: 6px;
      height: 6px;
      background: radial-gradient(
        circle,
        rgba(0, 188, 212, 0.5),
        transparent 60%
      );
      border-radius: 50%;
      animation: twinkle 2s ease-in-out infinite alternate;
    }
  }

  .tiny-particle {
    width: 2px;
    height: 2px;
    background: rgba(255, 255, 255, 0.6);
    box-shadow: 0 0 4px rgba(255, 255, 255, 0.3);
    animation-direction: reverse;

    &::before {
      content: '';
      position: absolute;
      top: -0.5px;
      left: -0.5px;
      width: 3px;
      height: 3px;
      background: radial-gradient(
        circle,
        rgba(124, 77, 255, 0.4),
        transparent 50%
      );
      border-radius: 50%;
      animation: sparkle 1.5s ease-in-out infinite;
    }
  }
}

/* 动画定义 */
@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0.7;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.2);
    opacity: 0;
  }
}

@keyframes shimmer {
  0% {
    transform: translateX(-20px);
  }
  100% {
    transform: translateX(240px);
  }
}

@keyframes orbit {
  from {
    transform: translate(-50%, -50%) rotate(var(--angle))
      translateY(calc(-1 * var(--radius))) rotate(calc(-1 * var(--angle)));
  }
  to {
    transform: translate(-50%, -50%) rotate(calc(var(--angle) + 360deg))
      translateY(calc(-1 * var(--radius)))
      rotate(calc(-1 * (var(--angle) + 360deg)));
  }
}

@keyframes pulse-glow {
  0% {
    opacity: 0.3;
    transform: scale(0.8);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.2);
  }
  100% {
    opacity: 0.3;
    transform: scale(0.8);
  }
}

@keyframes twinkle {
  0% {
    opacity: 0.2;
    transform: scale(0.5) rotate(0deg);
  }
  50% {
    opacity: 0.9;
    transform: scale(1.3) rotate(180deg);
  }
  100% {
    opacity: 0.2;
    transform: scale(0.5) rotate(360deg);
  }
}

@keyframes sparkle {
  0%,
  100% {
    opacity: 0;
    transform: scale(0);
  }
  50% {
    opacity: 1;
    transform: scale(1.5);
  }
}

@keyframes shimmer-avatar {
  0% {
    left: -100%;
  }
  50% {
    left: 100%;
  }
  100% {
    left: 100%;
  }
}

/* 响应式设计 */
@media (max-width: 480px) {
  .loading-content {
    padding: 20px;
  }

  .avatar-ring {
    width: 100px;
    height: 100px;
  }

  .pulse-ring {
    &.pulse-ring-1 {
      width: 120px;
      height: 120px;
    }
    &.pulse-ring-2 {
      width: 140px;
      height: 140px;
    }
    &.pulse-ring-3 {
      width: 160px;
      height: 160px;
    }
  }

  .loading-title {
    font-size: 20px;
  }

  .progress-container {
    width: 200px;
  }

  .particles {
    width: 300px;
    height: 300px;

    .main-particle {
      width: 4px;
      height: 4px;
    }

    .secondary-particle {
      width: 3px;
      height: 3px;
    }
  }
}
</style>

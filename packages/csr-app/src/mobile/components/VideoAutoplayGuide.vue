<template>
  <div v-if="showGuide" class="video-autoplay-guide" @click="handleInteraction">
    <!-- 播放按钮引导 -->
    <div v-if="guideType === 'play'" class="guide-content play-guide">
      <div class="play-button">
        <div class="play-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M8 5v14l11-7z" />
          </svg>
        </div>
      </div>
      <div class="guide-text">
        <h3>Tap to Play Video</h3>
        <p
          >Browser security policy requires manual interaction to start
          playback</p
        >
      </div>
    </div>

    <!-- 音频开启引导 -->
    <div v-if="guideType === 'audio'" class="guide-content audio-guide">
      <div class="audio-button">
        <div class="audio-icon">
          <svg viewBox="0 0 24 24" fill="currentColor">
            <path
              d="M3 9v6h4l5 5V4L7 9H3zm13.5 3c0-1.77-1.02-3.29-2.5-4.03v8.05c1.48-.73 2.5-2.25 2.5-4.02zM14 3.23v2.06c2.89.86 5 3.54 5 6.71s-2.11 5.85-5 6.71v2.06c4.01-.91 7-4.49 7-8.77s-2.99-7.86-7-8.77z"
            />
          </svg>
        </div>
      </div>
      <div class="guide-text">
        <h3>Tap to Enable Sound</h3>
        <p>Video is playing muted. Tap to enable sound for better experience</p>
      </div>
    </div>

    <!-- 通用交互引导 -->
    <div
      v-if="guideType === 'interaction'"
      class="guide-content interaction-guide"
    >
      <div class="interaction-icon">
        <svg viewBox="0 0 24 24" fill="currentColor">
          <path
            d="M7 4V2C7 1.45 7.45 1 8 1S9 1.55 9 2V4H15V2C15 1.45 15.45 1 16 1S17 1.55 17 2V4H19C20.1 4 21 4.9 21 6V20C21 21.1 20.1 22 19 22H5C3.9 22 3 21.1 3 20V6C3 4.9 3.9 4 5 4H7Z"
          />
        </svg>
      </div>
      <div class="guide-text">
        <h3>Tap to Continue</h3>
        <p>User interaction required to start video playback</p>
      </div>
    </div>

    <!-- 脉冲动画背景 -->
    <div class="pulse-bg"></div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'

interface Props {
  type: 'play' | 'audio' | 'interaction'
  show?: boolean
  autoHide?: boolean
  autoHideDelay?: number
}

const props = withDefaults(defineProps<Props>(), {
  show: true,
  autoHide: false,
  autoHideDelay: 5000,
})

const emit = defineEmits<{
  'user-interaction': []
  'hide': []
}>()

const showGuide = ref(props.show)
const guideType = computed(() => props.type)

// 处理用户交互
const handleInteraction = () => {
  showGuide.value = false
  emit('user-interaction')
  emit('hide')
}

// 自动隐藏
if (props.autoHide) {
  setTimeout(() => {
    if (showGuide.value) {
      showGuide.value = false
      emit('hide')
    }
  }, props.autoHideDelay)
}

// 暴露方法
defineExpose({
  show: () => {
    showGuide.value = true
  },
  hide: () => {
    showGuide.value = false
  },
})
</script>

<style scoped>
.video-autoplay-guide {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  cursor: pointer;
  backdrop-filter: blur(4px);
}

.guide-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  color: white;
  padding: 2rem;
  max-width: 300px;
}

.play-button,
.audio-button {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  border: 2px solid rgba(255, 255, 255, 0.3);
  transition: all 0.3s ease;
}

.play-button:hover,
.audio-button:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: scale(1.1);
}

.play-icon,
.audio-icon {
  width: 32px;
  height: 32px;
  color: white;
}

.interaction-icon {
  width: 60px;
  height: 60px;
  color: white;
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.guide-text h3 {
  margin: 0 0 0.5rem 0;
  font-size: 1.2rem;
  font-weight: 600;
}

.guide-text p {
  margin: 0;
  font-size: 0.9rem;
  opacity: 0.8;
  line-height: 1.4;
}

.pulse-bg {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  border-radius: 50%;
  background: radial-gradient(
    circle,
    rgba(255, 255, 255, 0.1) 0%,
    transparent 70%
  );
  animation: pulse 2s infinite;
  z-index: -1;
}

@keyframes pulse {
  0% {
    transform: translate(-50%, -50%) scale(0.8);
    opacity: 1;
  }
  100% {
    transform: translate(-50%, -50%) scale(1.5);
    opacity: 0;
  }
}

/* 不同类型的特殊样式 */
.play-guide .play-button {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-color: rgba(255, 255, 255, 0.5);
}

.audio-guide .audio-button {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  border-color: rgba(255, 255, 255, 0.5);
}

.interaction-guide {
  animation: gentle-bounce 2s infinite;
}

@keyframes gentle-bounce {
  0%,
  100% {
    transform: translateY(0);
  }
  50% {
    transform: translateY(-10px);
  }
}
</style>

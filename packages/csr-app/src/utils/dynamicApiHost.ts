/**
 * 获取动态API地址的工具函数
 * 根据当前域名动态构造API地址，支持多域名部署
 */

// 声明全局类型
declare global {
  interface Window {
    __DYNAMIC_API_HOST__?: string
  }
}

/**
 * 获取动态API地址
 * 优先级：
 * 1. 环境变量 VITE_API_HOST（在iframe中优先使用，避免被主应用影响）
 * 2. 全局变量 window.__DYNAMIC_API_HOST__（由Vite插件注入）
 * 3. meta标签中的api-host（由Vite插件注入）
 */
export function getDynamicApiHost(): string {
  // 检测是否在iframe中运行
  const isInIframe = typeof window !== 'undefined' && window.self !== window.top

  // 在iframe中优先使用环境变量，避免被主应用的配置影响
  if (isInIframe && import.meta.env.VITE_API_HOST) {
    console.log(
      '🔧 iframe环境: 使用环境变量API地址',
      import.meta.env.VITE_API_HOST,
    )
    return import.meta.env.VITE_API_HOST
  }

  // 非iframe环境或没有环境变量时，使用动态配置
  if (typeof window !== 'undefined' && window.__DYNAMIC_API_HOST__) {
    console.log('🔧 使用全局变量API地址', window.__DYNAMIC_API_HOST__)
    return window.__DYNAMIC_API_HOST__
  }

  // 如果没有动态地址，尝试从meta标签获取
  if (typeof window !== 'undefined') {
    const metaTag = document.querySelector('meta[name="api-host"]')
    if (metaTag) {
      const apiHost =
        metaTag.getAttribute('content') || import.meta.env.VITE_API_HOST
      console.log('🔧 使用meta标签API地址', apiHost)
      return apiHost
    }
  }

  // 最终回退到环境变量
  console.log('🔧 使用环境变量API地址（回退）', import.meta.env.VITE_API_HOST)
  return import.meta.env.VITE_API_HOST
}

/**
 * 构造完整的API URL
 * @param path API路径，如 '/api/v1/user.whoami'
 * @returns 完整的API URL
 */
export function buildApiUrl(path: string): string {
  const apiHost = getDynamicApiHost()
  // 确保路径以 / 开头
  const normalizedPath = path.startsWith('/') ? path : `/${path}`
  return `${apiHost}${normalizedPath}`
}

/**
 * 获取当前使用的API域名（不包含协议）
 * @returns API域名，如 'api.playshot.ai'
 */
export function getApiDomain(): string {
  const apiHost = getDynamicApiHost()
  try {
    const url = new URL(apiHost)
    return url.hostname
  } catch {
    return apiHost.replace(/^https?:\/\//, '')
  }
}

/**
 * 统一音频管理器
 * 统一管理TTS、BGM、视频音频的状态和切换，解决音频冲突问题
 */

import { ref, type Ref } from 'vue'
import type { AudioState } from './types'

export interface AudioConfig {
  ttsVolume: number
  bgmVolume: number
  videoVolume: number
  fadeInDuration: number
  fadeOutDuration: number
  duckingLevel: number // BGM降低的音量比例
}

export interface AudioStateCallbacks {
  onTTSStart?: (message: string) => void
  onTTSEnd?: (message: string) => void
  onBGMStart?: () => void
  onBGMEnd?: () => void
  onVolumeChange?: (type: 'tts' | 'bgm' | 'video', volume: number) => void
  onError?: (error: Error, context: string) => void
}

export class AudioStateManager {
  private state: Ref<AudioState>
  private config: AudioConfig
  private callbacks: AudioStateCallbacks
  
  // 音频实例引用
  private ttsAudio: HTMLAudioElement | null = null
  private bgmAudio: HTMLAudioElement | null = null
  private currentVideoElement: HTMLVideoElement | null = null
  
  // 音量控制
  private fadeTimers = new Map<string, number>()
  private originalBgmVolume = 1.0
  
  // TTS队列管理
  private ttsQueue: Array<{ message: string; audioUrl: string }> = []
  private isProcessingTTS = false
  
  constructor(
    config: Partial<AudioConfig> = {},
    callbacks: AudioStateCallbacks = {}
  ) {
    this.config = {
      ttsVolume: 1.0,
      bgmVolume: 0.8,
      videoVolume: 1.0,
      fadeInDuration: 500,
      fadeOutDuration: 300,
      duckingLevel: 0.3,
      ...config,
    }
    
    this.callbacks = callbacks
    
    // 初始化状态
    this.state = ref({
      tts: {
        isPlaying: false,
        currentMessage: undefined,
        queue: [],
      },
      bgm: {
        isPlaying: false,
        volume: this.config.bgmVolume,
        originalVolume: this.config.bgmVolume,
      },
      video: {
        hasAudio: false,
        isMuted: true,
      },
    })
    
    console.log('🔊 AudioStateManager initialized')
  }
  
  /**
   * 设置音频元素引用
   */
  setAudioElements(
    tts?: HTMLAudioElement,
    bgm?: HTMLAudioElement
  ): void {
    if (tts) {
      this.ttsAudio = tts
      this.setupTTSEvents()
    }
    
    if (bgm) {
      this.bgmAudio = bgm
      this.setupBGMEvents()
    }
  }
  
  /**
   * 设置当前视频元素
   */
  setCurrentVideoElement(video: HTMLVideoElement | null): void {
    this.currentVideoElement = video
    
    if (video) {
      this.setupVideoEvents(video)
      this.detectVideoAudio(video)
    } else {
      this.updateVideoState({ hasAudio: false, isMuted: true })
    }
  }
  
  /**
   * 播放TTS
   */
  async playTTS(message: string, audioUrl: string): Promise<void> {
    // 添加到队列
    this.ttsQueue.push({ message, audioUrl })
    this.updateTTSState({ queue: this.ttsQueue.map(item => item.message) })
    
    // 如果没有在处理TTS，开始处理
    if (!this.isProcessingTTS) {
      await this.processTTSQueue()
    }
  }
  
  /**
   * 停止TTS
   */
  stopTTS(): void {
    if (this.ttsAudio) {
      this.ttsAudio.pause()
      this.ttsAudio.currentTime = 0
    }
    
    // 清空队列
    this.ttsQueue = []
    this.isProcessingTTS = false
    
    this.updateTTSState({
      isPlaying: false,
      currentMessage: undefined,
      queue: [],
    })
    
    // 恢复BGM音量
    this.restoreBGMVolume()
    
    console.log('🔇 TTS stopped')
  }
  
  /**
   * 播放BGM
   */
  async playBGM(url?: string): Promise<void> {
    if (!this.bgmAudio) return
    
    try {
      if (url && this.bgmAudio.src !== url) {
        this.bgmAudio.src = url
      }
      
      this.bgmAudio.volume = this.config.bgmVolume
      this.originalBgmVolume = this.config.bgmVolume
      
      await this.bgmAudio.play()
      
      this.updateBGMState({
        isPlaying: true,
        volume: this.config.bgmVolume,
        originalVolume: this.config.bgmVolume,
      })
      
      if (this.callbacks.onBGMStart) {
        this.callbacks.onBGMStart()
      }
      
      console.log('🎵 BGM started')
      
    } catch (error) {
      console.error('Failed to play BGM:', error)
      if (this.callbacks.onError) {
        this.callbacks.onError(error as Error, 'BGM playback')
      }
    }
  }
  
  /**
   * 停止BGM
   */
  stopBGM(): void {
    if (this.bgmAudio) {
      this.bgmAudio.pause()
      this.bgmAudio.currentTime = 0
    }
    
    this.updateBGMState({
      isPlaying: false,
      volume: 0,
    })
    
    if (this.callbacks.onBGMEnd) {
      this.callbacks.onBGMEnd()
    }
    
    console.log('🔇 BGM stopped')
  }
  
  /**
   * 暂停BGM
   */
  pauseBGM(): void {
    if (this.bgmAudio && this.state.value.bgm.isPlaying) {
      this.bgmAudio.pause()
      this.updateBGMState({ isPlaying: false })
      console.log('⏸️ BGM paused')
    }
  }
  
  /**
   * 恢复BGM
   */
  async resumeBGM(): Promise<void> {
    if (this.bgmAudio && !this.state.value.bgm.isPlaying) {
      try {
        await this.bgmAudio.play()
        this.updateBGMState({ isPlaying: true })
        console.log('▶️ BGM resumed')
      } catch (error) {
        console.error('Failed to resume BGM:', error)
      }
    }
  }
  
  /**
   * 设置音量
   */
  setVolume(type: 'tts' | 'bgm' | 'video', volume: number): void {
    volume = Math.max(0, Math.min(1, volume)) // 限制在0-1之间
    
    switch (type) {
      case 'tts':
        this.config.ttsVolume = volume
        if (this.ttsAudio) {
          this.ttsAudio.volume = volume
        }
        break
        
      case 'bgm':
        this.config.bgmVolume = volume
        this.originalBgmVolume = volume
        if (this.bgmAudio) {
          this.bgmAudio.volume = volume
        }
        this.updateBGMState({ volume, originalVolume: volume })
        break
        
      case 'video':
        this.config.videoVolume = volume
        if (this.currentVideoElement) {
          this.currentVideoElement.volume = volume
        }
        break
    }
    
    if (this.callbacks.onVolumeChange) {
      this.callbacks.onVolumeChange(type, volume)
    }
  }
  
  /**
   * 静音/取消静音
   */
  setMuted(type: 'tts' | 'bgm' | 'video', muted: boolean): void {
    switch (type) {
      case 'tts':
        if (this.ttsAudio) {
          this.ttsAudio.muted = muted
        }
        break
        
      case 'bgm':
        if (this.bgmAudio) {
          this.bgmAudio.muted = muted
        }
        break
        
      case 'video':
        if (this.currentVideoElement) {
          this.currentVideoElement.muted = muted
        }
        this.updateVideoState({ isMuted: muted })
        break
    }
  }
  
  /**
   * 处理TTS队列
   */
  private async processTTSQueue(): Promise<void> {
    if (this.isProcessingTTS || this.ttsQueue.length === 0) return
    
    this.isProcessingTTS = true
    
    while (this.ttsQueue.length > 0) {
      const { message, audioUrl } = this.ttsQueue.shift()!
      
      try {
        await this.playTTSAudio(message, audioUrl)
      } catch (error) {
        console.error('Failed to play TTS:', error)
        if (this.callbacks.onError) {
          this.callbacks.onError(error as Error, 'TTS playback')
        }
      }
      
      // 更新队列状态
      this.updateTTSState({ queue: this.ttsQueue.map(item => item.message) })
    }
    
    this.isProcessingTTS = false
  }
  
  /**
   * 播放单个TTS音频
   */
  private async playTTSAudio(message: string, audioUrl: string): Promise<void> {
    if (!this.ttsAudio) {
      throw new Error('TTS audio element not available')
    }
    
    return new Promise((resolve, reject) => {
      const handleEnded = () => {
        cleanup()
        this.updateTTSState({
          isPlaying: false,
          currentMessage: undefined,
        })
        this.restoreBGMVolume()
        
        if (this.callbacks.onTTSEnd) {
          this.callbacks.onTTSEnd(message)
        }
        
        resolve()
      }
      
      const handleError = () => {
        cleanup()
        reject(new Error('TTS playback failed'))
      }
      
      const cleanup = () => {
        this.ttsAudio!.removeEventListener('ended', handleEnded)
        this.ttsAudio!.removeEventListener('error', handleError)
      }
      
      // 设置事件监听
      this.ttsAudio.addEventListener('ended', handleEnded, { once: true })
      this.ttsAudio.addEventListener('error', handleError, { once: true })
      
      // 降低BGM音量
      this.duckBGMVolume()
      
      // 播放TTS
      this.ttsAudio.src = audioUrl
      this.ttsAudio.volume = this.config.ttsVolume
      this.ttsAudio.play().then(() => {
        this.updateTTSState({
          isPlaying: true,
          currentMessage: message,
        })
        
        if (this.callbacks.onTTSStart) {
          this.callbacks.onTTSStart(message)
        }
      }).catch(reject)
    })
  }
  
  /**
   * 降低BGM音量（ducking）
   */
  private duckBGMVolume(): void {
    if (!this.bgmAudio || !this.state.value.bgm.isPlaying) return
    
    const targetVolume = this.originalBgmVolume * this.config.duckingLevel
    this.fadeVolume('bgm', targetVolume, this.config.fadeOutDuration)
  }
  
  /**
   * 恢复BGM音量
   */
  private restoreBGMVolume(): void {
    if (!this.bgmAudio || !this.state.value.bgm.isPlaying) return
    
    this.fadeVolume('bgm', this.originalBgmVolume, this.config.fadeInDuration)
  }
  
  /**
   * 音量渐变
   */
  private fadeVolume(type: 'bgm', targetVolume: number, duration: number): void {
    const audio = this.bgmAudio!
    const startVolume = audio.volume
    const volumeDiff = targetVolume - startVolume
    const steps = Math.ceil(duration / 16) // ~60fps
    const stepSize = volumeDiff / steps
    
    // 清除之前的渐变
    const timerId = this.fadeTimers.get(type)
    if (timerId) {
      clearInterval(timerId)
    }
    
    let currentStep = 0
    const fadeInterval = setInterval(() => {
      currentStep++
      const newVolume = startVolume + (stepSize * currentStep)
      
      if (currentStep >= steps) {
        audio.volume = targetVolume
        this.updateBGMState({ volume: targetVolume })
        clearInterval(fadeInterval)
        this.fadeTimers.delete(type)
      } else {
        audio.volume = newVolume
        this.updateBGMState({ volume: newVolume })
      }
    }, 16)
    
    this.fadeTimers.set(type, fadeInterval)
  }
  
  /**
   * 检测视频是否有音频
   */
  private detectVideoAudio(video: HTMLVideoElement): void {
    // 简单的音频检测逻辑
    const hasAudio = !video.muted && video.volume > 0
    this.updateVideoState({ hasAudio })
    
    // 如果视频有音频，降低BGM音量
    if (hasAudio) {
      this.duckBGMVolume()
    } else {
      this.restoreBGMVolume()
    }
  }
  
  /**
   * 设置TTS事件监听
   */
  private setupTTSEvents(): void {
    if (!this.ttsAudio) return
    
    this.ttsAudio.addEventListener('loadstart', () => {
      console.log('🔊 TTS loading started')
    })
    
    this.ttsAudio.addEventListener('canplaythrough', () => {
      console.log('🔊 TTS ready to play')
    })
  }
  
  /**
   * 设置BGM事件监听
   */
  private setupBGMEvents(): void {
    if (!this.bgmAudio) return
    
    this.bgmAudio.addEventListener('ended', () => {
      this.updateBGMState({ isPlaying: false })
      if (this.callbacks.onBGMEnd) {
        this.callbacks.onBGMEnd()
      }
    })
    
    this.bgmAudio.addEventListener('error', (error) => {
      console.error('BGM error:', error)
      if (this.callbacks.onError) {
        this.callbacks.onError(new Error('BGM playback error'), 'BGM')
      }
    })
  }
  
  /**
   * 设置视频事件监听
   */
  private setupVideoEvents(video: HTMLVideoElement): void {
    video.addEventListener('volumechange', () => {
      this.detectVideoAudio(video)
    })
    
    video.addEventListener('play', () => {
      this.detectVideoAudio(video)
    })
    
    video.addEventListener('pause', () => {
      this.updateVideoState({ hasAudio: false })
      this.restoreBGMVolume()
    })
  }
  
  /**
   * 更新TTS状态
   */
  private updateTTSState(updates: Partial<AudioState['tts']>): void {
    this.state.value = {
      ...this.state.value,
      tts: { ...this.state.value.tts, ...updates },
    }
  }
  
  /**
   * 更新BGM状态
   */
  private updateBGMState(updates: Partial<AudioState['bgm']>): void {
    this.state.value = {
      ...this.state.value,
      bgm: { ...this.state.value.bgm, ...updates },
    }
  }
  
  /**
   * 更新视频状态
   */
  private updateVideoState(updates: Partial<AudioState['video']>): void {
    this.state.value = {
      ...this.state.value,
      video: { ...this.state.value.video, ...updates },
    }
  }
  
  /**
   * 获取当前状态
   */
  get currentState(): AudioState {
    return { ...this.state.value }
  }
  
  /**
   * 获取状态的响应式引用
   */
  get stateRef(): Ref<AudioState> {
    return this.state
  }
  
  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopTTS()
    this.stopBGM()
    
    // 清除所有渐变定时器
    this.fadeTimers.forEach(timerId => clearInterval(timerId))
    this.fadeTimers.clear()
    
    this.ttsAudio = null
    this.bgmAudio = null
    this.currentVideoElement = null
    
    console.log('🔥 AudioStateManager destroyed')
  }
}

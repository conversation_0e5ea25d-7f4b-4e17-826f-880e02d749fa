# Nuxt 应用环境变量 - ReelPlay 正式环境

# API 配置
NUXT_PUBLIC_API_BASE=https://api.reelplay.ai
NUXT_PUBLIC_CSR_APP_URL=https://chat.reelplay.ai

# 应用配置
NUXT_PUBLIC_APP_NAME=ReelPlay
NUXT_PUBLIC_WEBSITE_TITLE=ReelPlay
NUXT_PUBLIC_LOGO_URL=https://static.reelplay.ai/static/images/logo/reelplay_logo.png
NUXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NUXT_PUBLIC_CDN_URL=https://static.reelplay.ai
NUXT_PUBLIC_STATIC_URL=https://static.reelplay.ai
NUXT_PUBLIC_GA_ID=AW-17411934868
NUXT_PUBLIC_VOLC_APP_ID=20005873

# 支付配置
NUXT_PUBLIC_PAYMENT_PROVIDER=stripe
NUXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_live_51RpgzV86m3BuPrklxH4jDePezLS0el9Y1GrEkDBRK9rf9TyZsfTLitsrio5EkcPsV1rZBBjq8g0lObfK2e6sPdXe00b6Y7NDUb

# 部署环境
NODE_ENV=production
DEPLOYMENT_ENV=production-reelplay
NUXT_PUBLIC_DEPLOYMENT_ENV=production-reelplay

/**
 * 直播背景循环管理器
 * 专门处理直播背景视频的无缝循环播放，解决原有循环中断问题
 */

import { ref, nextTick, type Ref } from 'vue'
import type {
  MediaResource,
  MediaState,
  LiveBackgroundConfig,
  EventPriority,
} from './types'
import type { MediaStateManager } from './MediaStateManager'

export interface LiveStreamBackgroundCallbacks {
  onVideoSwitch?: (fromIndex: number, toIndex: number) => void
  onLoopComplete?: (loopCount: number) => void
  onError?: (error: Error, context: string) => void
}

export class LiveStreamBackgroundManager {
  private mediaManager: MediaStateManager
  private callbacks: LiveStreamBackgroundCallbacks

  // 视频元素引用
  private primaryVideoRef: Ref<HTMLVideoElement | null> = ref(null)
  private secondaryVideoRef: Ref<HTMLVideoElement | null> = ref(null)

  // 状态管理
  private currentVideoType: 'primary' | 'secondary' = 'primary'
  private videoReadyState = ref({ primary: false, secondary: false })
  private isInitialized = false

  // 循环控制
  private loopCount = 0
  private switchTimer: number | null = null
  private preloadTimer: number | null = null

  // 配置
  private config: LiveBackgroundConfig | null = null
  private seamlessTransitionTime = 100 // ms，提前切换时间

  constructor(
    mediaManager: MediaStateManager,
    callbacks: LiveStreamBackgroundCallbacks = {},
  ) {
    this.mediaManager = mediaManager
    this.callbacks = callbacks
  }

  /**
   * 设置视频元素引用
   */
  setVideoElements(
    primary: HTMLVideoElement,
    secondary: HTMLVideoElement,
  ): void {
    this.primaryVideoRef.value = primary
    this.secondaryVideoRef.value = secondary
    this.setupVideoElements()
  }

  /**
   * 设置视频元素的事件监听
   */
  private setupVideoElements(): void {
    const primary = this.primaryVideoRef.value
    const secondary = this.secondaryVideoRef.value

    if (!primary || !secondary) return

    // 主视频事件
    primary.addEventListener('canplaythrough', () =>
      this.handleCanPlayThrough('primary'),
    )
    primary.addEventListener('ended', () => this.handleVideoEnded('primary'))
    primary.addEventListener('error', (e) =>
      this.handleVideoError('primary', e),
    )
    primary.addEventListener('loadedmetadata', () =>
      this.handleLoadedMetadata('primary'),
    )

    // 副视频事件
    secondary.addEventListener('canplaythrough', () =>
      this.handleCanPlayThrough('secondary'),
    )
    secondary.addEventListener('ended', () =>
      this.handleVideoEnded('secondary'),
    )
    secondary.addEventListener('error', (e) =>
      this.handleVideoError('secondary', e),
    )
    secondary.addEventListener('loadedmetadata', () =>
      this.handleLoadedMetadata('secondary'),
    )

    this.isInitialized = true
  }

  /**
   * 开始直播背景循环
   */
  async startLiveBackground(videoGroup: MediaResource[]): Promise<void> {
    if (!this.isInitialized) {
      throw new Error('Video elements not initialized')
    }

    if (!videoGroup || videoGroup.length === 0) {
      throw new Error('Invalid video group')
    }

    // 停止当前循环
    this.stopLiveBackground()

    // 设置新配置
    this.config = {
      videoGroup,
      currentIndex: 0,
      isLooping: true,
      seamlessTransition: true,
    }

    this.loopCount = 0

    console.log(`🎥 Starting live background with ${videoGroup.length} videos`)

    // 预加载前两个视频
    await this.preloadInitialVideos()

    // 开始播放第一个视频
    await this.playCurrentVideo()
  }

  /**
   * 停止直播背景循环
   */
  stopLiveBackground(): void {
    if (!this.config) return

    console.log('🛑 Stopping live background')

    // 停止定时器
    this.clearTimers()

    // 停止视频播放
    this.pauseAllVideos()

    // 重置状态
    this.config = null
    this.currentVideoType = 'primary'
    this.videoReadyState.value = { primary: false, secondary: false }
    this.loopCount = 0
  }

  /**
   * 暂停直播背景
   */
  pauseLiveBackground(): void {
    if (!this.config) return

    this.config.isLooping = false
    this.clearTimers()
    this.pauseAllVideos()

    console.log('⏸️ Live background paused')
  }

  /**
   * 恢复直播背景
   */
  async resumeLiveBackground(): Promise<void> {
    if (!this.config) return

    this.config.isLooping = true
    await this.playCurrentVideo()

    console.log('▶️ Live background resumed')
  }

  /**
   * 预加载初始视频
   */
  private async preloadInitialVideos(): Promise<void> {
    if (!this.config) return

    const { videoGroup } = this.config

    // 预加载第一个视频到主视频元素
    await this.setVideoSource('primary', videoGroup[0])

    // 如果有第二个视频，预加载到副视频元素
    if (videoGroup.length > 1) {
      await this.setVideoSource('secondary', videoGroup[1])
    }
  }

  /**
   * 设置视频源
   */
  private async setVideoSource(
    videoType: 'primary' | 'secondary',
    resource: MediaResource,
  ): Promise<void> {
    const videoElement =
      videoType === 'primary'
        ? this.primaryVideoRef.value
        : this.secondaryVideoRef.value

    if (!videoElement) {
      throw new Error(`${videoType} video element not found`)
    }

    // 重置状态
    this.videoReadyState.value[videoType] = false

    // 设置视频源
    videoElement.src = resource.url
    videoElement.load()

    // 预播放（静音）
    try {
      videoElement.muted = true
      await videoElement.play()
      videoElement.pause()
      videoElement.currentTime = 0
    } catch (error) {
      console.warn(`Pre-play failed for ${videoType}:`, error)
    }
  }

  /**
   * 播放当前视频
   */
  private async playCurrentVideo(): Promise<void> {
    if (!this.config || !this.config.isLooping) return

    const activeVideo = this.getActiveVideoElement()
    if (!activeVideo) return

    try {
      activeVideo.currentTime = 0
      await activeVideo.play()

      // 调度下次切换
      if (activeVideo.duration) {
        this.scheduleNextSwitch(activeVideo.duration)
      }

      // 预加载下一个视频
      this.scheduleNextPreload()
    } catch (error) {
      console.error('Failed to play current video:', error)
      if (this.callbacks.onError) {
        this.callbacks.onError(error as Error, 'Playing current video')
      }
    }
  }

  /**
   * 调度下次切换
   */
  private scheduleNextSwitch(duration: number): void {
    this.clearSwitchTimer()

    // 提前切换，避免黑屏
    const switchTime = Math.max(
      0,
      (duration - this.seamlessTransitionTime / 1000) * 1000,
    )

    this.switchTimer = window.setTimeout(() => {
      this.performSeamlessSwitch()
    }, switchTime)
  }

  /**
   * 调度下一个视频的预加载
   */
  private scheduleNextPreload(): void {
    if (!this.config) return

    // 在当前视频播放到一半时开始预加载下一个
    const activeVideo = this.getActiveVideoElement()
    if (!activeVideo || !activeVideo.duration) return

    const preloadTime = (activeVideo.duration / 2) * 1000

    this.preloadTimer = window.setTimeout(() => {
      this.preloadNextVideo()
    }, preloadTime)
  }

  /**
   * 预加载下一个视频
   */
  private async preloadNextVideo(): Promise<void> {
    if (!this.config) return

    const nextIndex = this.getNextVideoIndex()
    const nextResource = this.config.videoGroup[nextIndex]
    const inactiveVideoType =
      this.currentVideoType === 'primary' ? 'secondary' : 'primary'

    try {
      await this.setVideoSource(inactiveVideoType, nextResource)
    } catch (error) {
      console.error('Failed to preload next video:', error)
    }
  }

  /**
   * 执行无缝切换
   */
  private async performSeamlessSwitch(): Promise<void> {
    if (!this.config || !this.config.isLooping) return

    const inactiveVideoType =
      this.currentVideoType === 'primary' ? 'secondary' : 'primary'
    const inactiveVideo =
      inactiveVideoType === 'primary'
        ? this.primaryVideoRef.value
        : this.secondaryVideoRef.value

    // 检查下一个视频是否准备好
    if (!inactiveVideo || !this.videoReadyState.value[inactiveVideoType]) {
      console.warn('Next video not ready, retrying current video')
      await this.retryCurrentVideo()
      return
    }

    const oldVideoType = this.currentVideoType

    // 切换到下一个视频
    this.currentVideoType = inactiveVideoType
    this.config.currentIndex = this.getNextVideoIndex()

    // 播放新视频
    try {
      inactiveVideo.currentTime = 0
      await inactiveVideo.play()

      // 调度下次切换
      if (inactiveVideo.duration) {
        this.scheduleNextSwitch(inactiveVideo.duration)
      }

      // 预加载下一个视频
      this.scheduleNextPreload()

      // 检查是否完成一轮循环
      if (this.config.currentIndex === 0) {
        this.loopCount++
        if (this.callbacks.onLoopComplete) {
          this.callbacks.onLoopComplete(this.loopCount)
        }
      }

      // 触发切换回调
      if (this.callbacks.onVideoSwitch) {
        this.callbacks.onVideoSwitch(
          oldVideoType === 'primary' ? 0 : 1,
          this.currentVideoType === 'primary' ? 0 : 1,
        )
      }
    } catch (error) {
      console.error('Failed to switch video:', error)
      await this.retryCurrentVideo()
    }
  }

  /**
   * 重试当前视频
   */
  private async retryCurrentVideo(): Promise<void> {
    const activeVideo = this.getActiveVideoElement()
    if (!activeVideo) return

    try {
      activeVideo.currentTime = 0
      await activeVideo.play()

      if (activeVideo.duration) {
        this.scheduleNextSwitch(activeVideo.duration)
      }
    } catch (error) {
      console.error('Failed to retry current video:', error)
    }
  }

  /**
   * 获取下一个视频索引
   */
  private getNextVideoIndex(): number {
    if (!this.config) return 0
    return (this.config.currentIndex + 1) % this.config.videoGroup.length
  }

  /**
   * 获取当前活跃的视频元素
   */
  private getActiveVideoElement(): HTMLVideoElement | null {
    return this.currentVideoType === 'primary'
      ? this.primaryVideoRef.value
      : this.secondaryVideoRef.value
  }

  /**
   * 获取当前播放的资源
   */
  private getCurrentResource(): MediaResource | null {
    if (!this.config || !this.config.videoGroup) return null
    return this.config.videoGroup[this.config.currentIndex] || null
  }

  /**
   * 暂停所有视频
   */
  private pauseAllVideos(): void {
    if (this.primaryVideoRef.value) {
      this.primaryVideoRef.value.pause()
    }
    if (this.secondaryVideoRef.value) {
      this.secondaryVideoRef.value.pause()
    }
  }

  /**
   * 清空所有定时器
   */
  private clearTimers(): void {
    this.clearSwitchTimer()
    if (this.preloadTimer) {
      clearTimeout(this.preloadTimer)
      this.preloadTimer = null
    }
  }

  /**
   * 清空切换定时器
   */
  private clearSwitchTimer(): void {
    if (this.switchTimer) {
      clearTimeout(this.switchTimer)
      this.switchTimer = null
    }
  }

  // 视频事件处理器
  private handleCanPlayThrough(videoType: 'primary' | 'secondary'): void {
    this.videoReadyState.value[videoType] = true
  }

  private handleVideoEnded(videoType: 'primary' | 'secondary'): void {
    // 检查是否是当前活跃视频结束
    if (videoType === this.currentVideoType && this.config?.isLooping) {
      // 获取刚结束的视频资源
      const endedResource = this.getCurrentResource()

      // 如果刚结束的视频是不可打断的，触发延迟事件处理
      if (endedResource?.not_loop && this.mediaManager) {
        console.log(
          '🎬 Non-interruptible video ended, processing pending events',
        )
        this.mediaManager.processPendingEvents()
      }

      // 执行正常的无缝切换
      this.performSeamlessSwitch()
    }
  }

  private handleVideoError(
    videoType: 'primary' | 'secondary',
    error: Event,
  ): void {
    console.error(`Video error on ${videoType}:`, error)
    this.videoReadyState.value[videoType] = false

    if (this.callbacks.onError) {
      this.callbacks.onError(
        new Error(`Video error on ${videoType}`),
        'Video playback',
      )
    }
  }

  private handleLoadedMetadata(videoType: 'primary' | 'secondary'): void {
    // 元数据加载完成，可以开始预播放
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.stopLiveBackground()
    this.primaryVideoRef.value = null
    this.secondaryVideoRef.value = null
  }
}

/**
 * 资源管理器
 * 优化资源预加载策略，避免带宽竞争，提升用户体验
 */

import { ref, type Ref } from 'vue'
import type { MediaResource, PreloadStrategy } from './types'
import { EventPriority } from './types'

export interface ResourceCacheItem {
  resource: MediaResource
  blob?: Blob
  objectUrl?: string
  loadTime: number
  accessTime: number
  accessCount: number
  size?: number
}

export interface ResourceManagerCallbacks {
  onResourceLoaded?: (resource: MediaResource) => void
  onResourceCached?: (resource: MediaResource, cacheSize: number) => void
  onResourceEvicted?: (resource: MediaResource) => void
  onError?: (error: Error, context: string) => void
}

export class ResourceManager {
  private strategy: PreloadStrategy
  private callbacks: ResourceManagerCallbacks

  // 缓存管理
  private cache = new Map<string, ResourceCacheItem>()
  private loadingPromises = new Map<string, Promise<void>>()
  private preloadQueue: MediaResource[] = []

  // 状态管理
  private isPreloading = false
  private currentConcurrentLoads = 0
  private totalCacheSize = 0
  private maxCacheSize = 100 * 1024 * 1024 // 100MB

  // 统计信息
  private stats = ref({
    totalLoaded: 0,
    totalCached: 0,
    totalEvicted: 0,
    cacheHitRate: 0,
    averageLoadTime: 0,
  })

  constructor(
    strategy: Partial<PreloadStrategy> = {},
    callbacks: ResourceManagerCallbacks = {},
  ) {
    this.strategy = {
      maxConcurrent: 2,
      priorityThreshold: EventPriority.NORMAL,
      cacheSize: 10,
      prefetchNext: true,
      ...strategy,
    }

    this.callbacks = callbacks

    // 开始预加载处理循环
    this.startPreloadProcessing()

    console.log('📦 ResourceManager initialized', this.strategy)
  }

  /**
   * 预加载资源
   */
  async preloadResource(resource: MediaResource): Promise<void> {
    const cacheKey = this.getCacheKey(resource)

    // 检查是否已经在缓存中
    if (this.cache.has(cacheKey)) {
      this.updateAccessTime(cacheKey)
      return
    }

    // 检查是否正在加载
    const existingPromise = this.loadingPromises.get(cacheKey)
    if (existingPromise) {
      return existingPromise
    }

    // 创建加载Promise
    const loadPromise = this.loadResource(resource)
    this.loadingPromises.set(cacheKey, loadPromise)

    try {
      await loadPromise
    } finally {
      this.loadingPromises.delete(cacheKey)
    }
  }

  /**
   * 批量预加载资源
   */
  async preloadResources(resources: MediaResource[]): Promise<void> {
    // 按优先级排序
    const sortedResources = resources.sort((a, b) => {
      const priorityA = a.priority || EventPriority.NORMAL
      const priorityB = b.priority || EventPriority.NORMAL
      return priorityA - priorityB
    })

    // 添加到预加载队列
    this.preloadQueue.push(...sortedResources)

    console.log(`📥 Added ${resources.length} resources to preload queue`)
  }

  /**
   * 获取资源
   */
  async getResource(resource: MediaResource): Promise<string> {
    const cacheKey = this.getCacheKey(resource)
    const cacheItem = this.cache.get(cacheKey)

    if (cacheItem) {
      // 缓存命中
      this.updateAccessTime(cacheKey)
      this.updateStats('hit')

      if (cacheItem.objectUrl) {
        return cacheItem.objectUrl
      }
    }

    // 缓存未命中，加载资源
    this.updateStats('miss')
    await this.preloadResource(resource)

    const updatedCacheItem = this.cache.get(cacheKey)
    return updatedCacheItem?.objectUrl || resource.url
  }

  /**
   * 清除资源缓存
   */
  clearCache(): void {
    // 释放所有对象URL
    this.cache.forEach((item) => {
      if (item.objectUrl) {
        URL.revokeObjectURL(item.objectUrl)
      }
    })

    this.cache.clear()
    this.totalCacheSize = 0

    console.log('🗑️ Resource cache cleared')
  }

  /**
   * 移除特定资源
   */
  removeResource(resource: MediaResource): void {
    const cacheKey = this.getCacheKey(resource)
    const cacheItem = this.cache.get(cacheKey)

    if (cacheItem) {
      if (cacheItem.objectUrl) {
        URL.revokeObjectURL(cacheItem.objectUrl)
      }

      this.totalCacheSize -= cacheItem.size || 0
      this.cache.delete(cacheKey)

      if (this.callbacks.onResourceEvicted) {
        this.callbacks.onResourceEvicted(resource)
      }
    }
  }

  /**
   * 开始预加载处理循环
   */
  private async startPreloadProcessing(): Promise<void> {
    if (this.isPreloading) return

    this.isPreloading = true

    while (this.isPreloading) {
      if (
        this.preloadQueue.length > 0 &&
        this.currentConcurrentLoads < this.strategy.maxConcurrent
      ) {
        const resource = this.preloadQueue.shift()!

        // 检查优先级阈值
        const resourcePriority = resource.priority || EventPriority.NORMAL
        if (resourcePriority <= this.strategy.priorityThreshold) {
          this.preloadResource(resource).catch((error) => {
            console.error('Preload failed:', error)
          })
        }
      } else {
        // 没有资源或达到并发限制，短暂等待
        await new Promise((resolve) => setTimeout(resolve, 100))
      }
    }
  }

  /**
   * 加载单个资源
   */
  private async loadResource(resource: MediaResource): Promise<void> {
    const cacheKey = this.getCacheKey(resource)
    const startTime = Date.now()

    this.currentConcurrentLoads++

    try {
      console.log(`📥 Loading resource: ${resource.url}`)

      const response = await fetch(resource.url)
      if (!response.ok) {
        throw new Error(`Failed to load resource: ${response.status}`)
      }

      const blob = await response.blob()
      const objectUrl = URL.createObjectURL(blob)
      const loadTime = Date.now() - startTime
      const size = blob.size

      // 检查缓存空间
      await this.ensureCacheSpace(size)

      // 添加到缓存
      const cacheItem: ResourceCacheItem = {
        resource,
        blob,
        objectUrl,
        loadTime,
        accessTime: Date.now(),
        accessCount: 1,
        size,
      }

      this.cache.set(cacheKey, cacheItem)
      this.totalCacheSize += size

      // 更新统计
      this.updateStats('loaded', loadTime)

      // 触发回调
      if (this.callbacks.onResourceLoaded) {
        this.callbacks.onResourceLoaded(resource)
      }

      if (this.callbacks.onResourceCached) {
        this.callbacks.onResourceCached(resource, this.cache.size)
      }

      console.log(
        `✅ Resource loaded: ${resource.url} (${this.formatSize(
          size,
        )}, ${loadTime}ms)`,
      )
    } catch (error) {
      console.error(`❌ Failed to load resource: ${resource.url}`, error)

      if (this.callbacks.onError) {
        this.callbacks.onError(
          error as Error,
          `Loading resource: ${resource.url}`,
        )
      }

      throw error
    } finally {
      this.currentConcurrentLoads--
    }
  }

  /**
   * 确保缓存空间足够
   */
  private async ensureCacheSpace(requiredSize: number): Promise<void> {
    // 检查是否需要清理缓存
    while (
      this.cache.size >= this.strategy.cacheSize ||
      this.totalCacheSize + requiredSize > this.maxCacheSize
    ) {
      await this.evictLeastRecentlyUsed()
    }
  }

  /**
   * 驱逐最少使用的资源
   */
  private async evictLeastRecentlyUsed(): Promise<void> {
    if (this.cache.size === 0) return

    let oldestKey = ''
    let oldestTime = Date.now()

    // 找到最久未访问的资源
    this.cache.forEach((item, key) => {
      if (item.accessTime < oldestTime) {
        oldestTime = item.accessTime
        oldestKey = key
      }
    })

    if (oldestKey) {
      const item = this.cache.get(oldestKey)!
      this.removeResource(item.resource)
      this.updateStats('evicted')

      console.log(`🗑️ Evicted resource: ${item.resource.url}`)
    }
  }

  /**
   * 更新访问时间
   */
  private updateAccessTime(cacheKey: string): void {
    const item = this.cache.get(cacheKey)
    if (item) {
      item.accessTime = Date.now()
      item.accessCount++
    }
  }

  /**
   * 获取缓存键
   */
  private getCacheKey(resource: MediaResource): string {
    return `${resource.type}_${resource.url}`
  }

  /**
   * 更新统计信息
   */
  private updateStats(
    type: 'loaded' | 'hit' | 'miss' | 'evicted',
    loadTime?: number,
  ): void {
    const currentStats = this.stats.value

    switch (type) {
      case 'loaded':
        currentStats.totalLoaded++
        currentStats.totalCached++
        if (loadTime) {
          currentStats.averageLoadTime =
            (currentStats.averageLoadTime * (currentStats.totalLoaded - 1) +
              loadTime) /
            currentStats.totalLoaded
        }
        break

      case 'hit':
        // 缓存命中率计算在 hit 和 miss 中更新
        break

      case 'miss': {
        // 缓存命中率 = 命中次数 / (命中次数 + 未命中次数)
        const totalAccess = currentStats.totalLoaded + 1 // 简化计算
        currentStats.cacheHitRate =
          (currentStats.totalCached / totalAccess) * 100
        break
      }

      case 'evicted':
        currentStats.totalEvicted++
        currentStats.totalCached--
        break
    }
  }

  /**
   * 格式化文件大小
   */
  private formatSize(bytes: number): string {
    const units = ['B', 'KB', 'MB', 'GB']
    let size = bytes
    let unitIndex = 0

    while (size >= 1024 && unitIndex < units.length - 1) {
      size /= 1024
      unitIndex++
    }

    return `${size.toFixed(1)}${units[unitIndex]}`
  }

  /**
   * 获取缓存统计信息
   */
  get cacheStats() {
    return {
      ...this.stats.value,
      cacheSize: this.cache.size,
      totalCacheSize: this.formatSize(this.totalCacheSize),
      currentLoads: this.currentConcurrentLoads,
      queueLength: this.preloadQueue.length,
    }
  }

  /**
   * 获取统计信息的响应式引用
   */
  get statsRef(): Ref<typeof this.stats.value> {
    return this.stats
  }

  /**
   * 销毁管理器
   */
  destroy(): void {
    this.isPreloading = false
    this.clearCache()
    this.preloadQueue = []
    this.loadingPromises.clear()

    console.log('🔥 ResourceManager destroyed')
  }
}

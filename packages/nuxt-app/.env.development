# Nuxt 应用开发环境变量

# API 配置
NUXT_PUBLIC_API_BASE=https://api-test.zhijianyuzhou.com
NUXT_PUBLIC_CSR_APP_URL=http://localhost:5173

# 应用配置
NUXT_PUBLIC_APP_NAME=ReelPlay
NUXT_PUBLIC_WEBSITE_TITLE=ReelPlay Dev
NUXT_PUBLIC_LOGO_URL=https://cdn.magiclight.ai/assets/playshot/logo-v2.png
NUXT_PUBLIC_SUPPORT_EMAIL=<EMAIL>
NUXT_PUBLIC_CDN_URL=https://static.reelplay.ai
NUXT_PUBLIC_STATIC_URL=https://static.reelplay.ai

# 统计分析配置
NUXT_PUBLIC_GA_ID=G-7J3CFG8D6T
NUXT_PUBLIC_ENABLE_GA=true

# 支付配置 (开发环境使用测试密钥)
NUXT_PUBLIC_PAYMENT_PROVIDER=stripe
NUXT_PUBLIC_STRIPE_PUBLIC_KEY=pk_test_51QU24xG7ZqVosOUplhEMVx9BLgirhPhUfAIyh9DTCEEoB4nYzS2gZZu4l76FM14wSJlDERe0oxB5iRSgyrHJKAc500edtUOUeF

# 部署环境
NODE_ENV=development
DEPLOYMENT_ENV=development
NUXT_PUBLIC_DEPLOYMENT_ENV=development

/**
 * 新的媒体管理系统类型定义
 * 解决原有架构中的事件冲突和状态混乱问题
 */

// 媒体类型枚举
export enum MediaType {
  LIVE_BACKGROUND = 'live_background', // 直播背景视频（循环）
  FOREGROUND_VIDEO = 'foreground_video', // 前景视频（用户交互触发）
  FOREGROUND_IMAGE = 'foreground_image', // 前景图片
  AUDIO_TTS = 'audio_tts', // TTS音频
  AUDIO_BGM = 'audio_bgm', // 背景音乐
}

// 媒体状态枚举
export enum MediaState {
  IDLE = 'idle',
  LOADING = 'loading',
  READY = 'ready',
  PLAYING = 'playing',
  PAUSED = 'paused',
  ENDED = 'ended',
  ERROR = 'error',
}

// 事件优先级枚举
export enum EventPriority {
  CRITICAL = 0, // 系统级事件（停止、错误）
  HIGH = 1, // 用户交互事件（前景媒体）
  NORMAL = 2, // 常规事件（消息、TTS）
  LOW = 3, // 背景事件（直播循环）
}

// 媒体资源接口
export interface MediaResource {
  id: string
  type: MediaType
  url: string
  duration?: number
  metadata?: Record<string, any>
  preloaded?: boolean
  priority?: number
  not_loop?: boolean // 是否不可被循环打断（重要视频）
}

// 媒体事件接口
export interface MediaEvent {
  id: string
  type: string
  priority: EventPriority
  mediaType: MediaType
  data: any
  timestamp: number
  abortController?: AbortController
}

// 直播背景循环配置
export interface LiveBackgroundConfig {
  videoGroup: MediaResource[]
  currentIndex: number
  isLooping: boolean
  seamlessTransition: boolean
}

// 前景媒体配置
export interface ForegroundMediaConfig {
  resource: MediaResource
  autoPlay: boolean
  showControls: boolean
  minWatchDuration?: number
}

// 音频状态接口
export interface AudioState {
  tts: {
    isPlaying: boolean
    currentMessage?: string
    queue: string[]
  }
  bgm: {
    isPlaying: boolean
    volume: number
    originalVolume: number
  }
  video: {
    hasAudio: boolean
    isMuted: boolean
  }
}

// 媒体管理器状态接口
export interface MediaManagerState {
  // 直播背景状态
  liveBackground: {
    state: MediaState
    config: LiveBackgroundConfig | null
    currentResource: MediaResource | null
  }

  // 前景媒体状态
  foreground: {
    state: MediaState
    config: ForegroundMediaConfig | null
    currentResource: MediaResource | null
  }

  // 音频状态
  audio: AudioState

  // 全局状态
  global: {
    isUserInteracting: boolean
    isPaused: boolean
    lastUserAction: number
  }
}

// 事件处理结果
export interface EventHandleResult {
  success: boolean
  message?: string
  shouldContinue?: boolean
  nextEvent?: MediaEvent
}

// 资源预加载策略
export interface PreloadStrategy {
  maxConcurrent: number
  priorityThreshold: EventPriority
  cacheSize: number
  prefetchNext: boolean
}

// 媒体管理器配置
export interface MediaManagerConfig {
  preloadStrategy: PreloadStrategy
  seamlessTransition: boolean
  autoRecovery: boolean
  debugMode: boolean
}

// 媒体管理器事件回调
export interface MediaManagerCallbacks {
  onStateChange?: (state: MediaManagerState) => void
  onError?: (error: Error, context: string) => void
  onResourceLoaded?: (resource: MediaResource) => void
  onEventProcessed?: (event: MediaEvent, result: EventHandleResult) => void
}

// 导出默认配置
export const DEFAULT_MEDIA_CONFIG: MediaManagerConfig = {
  preloadStrategy: {
    maxConcurrent: 2,
    priorityThreshold: EventPriority.NORMAL,
    cacheSize: 10,
    prefetchNext: true,
  },
  seamlessTransition: true,
  autoRecovery: true,
  debugMode: false,
}

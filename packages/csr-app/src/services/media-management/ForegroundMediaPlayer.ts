/**
 * 前景媒体播放器
 * 专门处理用户交互触发的前景视频和图片，确保与直播背景无冲突
 */

import { ref, type Ref } from 'vue'
import type {
  MediaResource,
  MediaState,
  ForegroundMediaConfig,
  MediaType,
} from './types'
import type { MediaStateManager } from './MediaStateManager'

export interface ForegroundMediaCallbacks {
  onMediaStart?: (resource: MediaResource) => void
  onMediaEnd?: (resource: MediaResource) => void
  onMediaSkip?: (resource: MediaResource, watchedDuration: number) => void
  onError?: (error: Error, context: string) => void
  onUserInteraction?: () => void
}

export class ForegroundMediaPlayer {
  private mediaManager: MediaStateManager
  private callbacks: ForegroundMediaCallbacks
  
  // 媒体元素引用
  private videoRef: Ref<HTMLVideoElement | null> = ref(null)
  private imageRef: Ref<HTMLImageElement | null> = ref(null)
  
  // 状态管理
  private currentResource: MediaResource | null = null
  private currentConfig: ForegroundMediaConfig | null = null
  private playStartTime = 0
  private minWatchDuration = 2000 // 最小观看时长（毫秒）
  
  // 控制状态
  private isPlaying = false
  private canSkip = false
  private skipTimer: number | null = null
  private abortController: AbortController | null = null
  
  constructor(
    mediaManager: MediaStateManager,
    callbacks: ForegroundMediaCallbacks = {}
  ) {
    this.mediaManager = mediaManager
    this.callbacks = callbacks
  }
  
  /**
   * 设置媒体元素引用
   */
  setMediaElements(
    video: HTMLVideoElement,
    image: HTMLImageElement
  ): void {
    this.videoRef.value = video
    this.imageRef.value = image
    this.setupMediaElements()
  }
  
  /**
   * 设置媒体元素事件监听
   */
  private setupMediaElements(): void {
    const video = this.videoRef.value
    const image = this.imageRef.value
    
    if (video) {
      video.addEventListener('loadedmetadata', () => this.handleVideoLoaded())
      video.addEventListener('ended', () => this.handleVideoEnded())
      video.addEventListener('error', (e) => this.handleVideoError(e))
      video.addEventListener('timeupdate', () => this.handleVideoTimeUpdate())
      video.addEventListener('click', () => this.handleUserInteraction())
    }
    
    if (image) {
      image.addEventListener('load', () => this.handleImageLoaded())
      image.addEventListener('error', (e) => this.handleImageError(e))
      image.addEventListener('click', () => this.handleUserInteraction())
    }
  }
  
  /**
   * 播放前景视频
   */
  async playForegroundVideo(
    resource: MediaResource,
    config: Partial<ForegroundMediaConfig> = {}
  ): Promise<void> {
    if (!this.videoRef.value) {
      throw new Error('Video element not initialized')
    }
    
    // 停止当前播放
    await this.stop()
    
    // 设置新的资源和配置
    this.currentResource = resource
    this.currentConfig = {
      resource,
      autoPlay: true,
      showControls: false,
      minWatchDuration: 2,
      ...config,
    }
    
    console.log('🎬 Playing foreground video:', resource.url)
    
    // 创建中止控制器
    this.abortController = new AbortController()
    
    try {
      // 暂停直播背景
      this.pauseLiveBackground()
      
      // 设置视频源
      this.videoRef.value.src = resource.url
      this.videoRef.value.load()
      
      // 等待视频加载
      await this.waitForVideoLoad()
      
      // 检查是否被中止
      if (this.abortController.signal.aborted) {
        throw new Error('Video playback was aborted')
      }
      
      // 开始播放
      this.playStartTime = Date.now()
      this.isPlaying = true
      this.canSkip = false
      
      await this.videoRef.value.play()
      
      // 设置最小观看时长定时器
      this.setMinWatchTimer()
      
      // 触发开始回调
      if (this.callbacks.onMediaStart) {
        this.callbacks.onMediaStart(resource)
      }
      
    } catch (error) {
      console.error('Failed to play foreground video:', error)
      await this.handlePlaybackError(error as Error)
      throw error
    }
  }
  
  /**
   * 显示前景图片
   */
  async showForegroundImage(
    resource: MediaResource,
    config: Partial<ForegroundMediaConfig> = {}
  ): Promise<void> {
    if (!this.imageRef.value) {
      throw new Error('Image element not initialized')
    }
    
    // 停止当前播放
    await this.stop()
    
    // 设置新的资源和配置
    this.currentResource = resource
    this.currentConfig = {
      resource,
      autoPlay: true,
      showControls: false,
      ...config,
    }
    
    console.log('🖼️ Showing foreground image:', resource.url)
    
    try {
      // 暂停直播背景
      this.pauseLiveBackground()
      
      // 设置图片源
      this.imageRef.value.src = resource.url
      
      // 等待图片加载
      await this.waitForImageLoad()
      
      // 触发开始回调
      if (this.callbacks.onMediaStart) {
        this.callbacks.onMediaStart(resource)
      }
      
    } catch (error) {
      console.error('Failed to show foreground image:', error)
      await this.handlePlaybackError(error as Error)
      throw error
    }
  }
  
  /**
   * 跳过当前媒体
   */
  async skip(): Promise<void> {
    if (!this.canSkip || !this.currentResource) {
      return
    }
    
    const watchedDuration = Date.now() - this.playStartTime
    
    console.log('⏭️ Skipping foreground media, watched:', watchedDuration, 'ms')
    
    // 触发跳过回调
    if (this.callbacks.onMediaSkip) {
      this.callbacks.onMediaSkip(this.currentResource, watchedDuration)
    }
    
    await this.stop()
  }
  
  /**
   * 停止当前播放
   */
  async stop(): Promise<void> {
    // 中止当前操作
    if (this.abortController) {
      this.abortController.abort()
      this.abortController = null
    }
    
    // 清空定时器
    this.clearTimers()
    
    // 停止视频播放
    if (this.videoRef.value && this.isPlaying) {
      this.videoRef.value.pause()
      this.videoRef.value.src = ''
    }
    
    // 清空图片
    if (this.imageRef.value) {
      this.imageRef.value.src = ''
    }
    
    // 重置状态
    const stoppedResource = this.currentResource
    this.currentResource = null
    this.currentConfig = null
    this.isPlaying = false
    this.canSkip = false
    this.playStartTime = 0
    
    // 恢复直播背景
    await this.resumeLiveBackground()
    
    // 触发结束回调
    if (stoppedResource && this.callbacks.onMediaEnd) {
      this.callbacks.onMediaEnd(stoppedResource)
    }
    
    console.log('🛑 Foreground media stopped')
  }
  
  /**
   * 等待视频加载
   */
  private waitForVideoLoad(): Promise<void> {
    return new Promise((resolve, reject) => {
      const video = this.videoRef.value!
      
      const handleLoad = () => {
        cleanup()
        resolve()
      }
      
      const handleError = (error: Event) => {
        cleanup()
        reject(new Error('Video load failed'))
      }
      
      const handleAbort = () => {
        cleanup()
        reject(new Error('Video load aborted'))
      }
      
      const cleanup = () => {
        video.removeEventListener('canplaythrough', handleLoad)
        video.removeEventListener('error', handleError)
        this.abortController?.signal.removeEventListener('abort', handleAbort)
      }
      
      video.addEventListener('canplaythrough', handleLoad, { once: true })
      video.addEventListener('error', handleError, { once: true })
      this.abortController?.signal.addEventListener('abort', handleAbort, { once: true })
      
      // 如果视频已经可以播放，立即解决
      if (video.readyState >= 4) {
        handleLoad()
      }
    })
  }
  
  /**
   * 等待图片加载
   */
  private waitForImageLoad(): Promise<void> {
    return new Promise((resolve, reject) => {
      const image = this.imageRef.value!
      
      const handleLoad = () => {
        cleanup()
        resolve()
      }
      
      const handleError = () => {
        cleanup()
        reject(new Error('Image load failed'))
      }
      
      const cleanup = () => {
        image.removeEventListener('load', handleLoad)
        image.removeEventListener('error', handleError)
      }
      
      image.addEventListener('load', handleLoad, { once: true })
      image.addEventListener('error', handleError, { once: true })
      
      // 如果图片已经加载，立即解决
      if (image.complete) {
        handleLoad()
      }
    })
  }
  
  /**
   * 设置最小观看时长定时器
   */
  private setMinWatchTimer(): void {
    const duration = this.currentConfig?.minWatchDuration || 2
    this.skipTimer = window.setTimeout(() => {
      this.canSkip = true
      console.log('✅ Can skip foreground media now')
    }, duration * 1000)
  }
  
  /**
   * 暂停直播背景
   */
  private pauseLiveBackground(): void {
    // 通过媒体管理器暂停直播背景
    this.mediaManager.addEvent({
      type: 'pause_live_background',
      priority: 1, // HIGH priority
      mediaType: 'live_background' as MediaType,
      data: { reason: 'foreground_media_playing' },
    })
  }
  
  /**
   * 恢复直播背景
   */
  private async resumeLiveBackground(): Promise<void> {
    // 通过媒体管理器恢复直播背景
    this.mediaManager.addEvent({
      type: 'resume_live_background',
      priority: 2, // NORMAL priority
      mediaType: 'live_background' as MediaType,
      data: { reason: 'foreground_media_ended' },
    })
  }
  
  /**
   * 处理播放错误
   */
  private async handlePlaybackError(error: Error): Promise<void> {
    console.error('Foreground media playback error:', error)
    
    if (this.callbacks.onError) {
      this.callbacks.onError(error, 'Foreground media playback')
    }
    
    // 清理状态并恢复直播背景
    await this.stop()
  }
  
  /**
   * 清空所有定时器
   */
  private clearTimers(): void {
    if (this.skipTimer) {
      clearTimeout(this.skipTimer)
      this.skipTimer = null
    }
  }
  
  // 媒体事件处理器
  private handleVideoLoaded(): void {
    console.log('📹 Foreground video loaded')
  }
  
  private async handleVideoEnded(): Promise<void> {
    console.log('🏁 Foreground video ended')
    await this.stop()
  }
  
  private handleVideoError(error: Event): void {
    this.handlePlaybackError(new Error('Video playback error'))
  }
  
  private handleVideoTimeUpdate(): void {
    // 可以在这里更新播放进度
  }
  
  private handleImageLoaded(): void {
    console.log('🖼️ Foreground image loaded')
  }
  
  private handleImageError(error: Event): void {
    this.handlePlaybackError(new Error('Image load error'))
  }
  
  private handleUserInteraction(): void {
    if (this.callbacks.onUserInteraction) {
      this.callbacks.onUserInteraction()
    }
    
    // 如果可以跳过，则跳过
    if (this.canSkip) {
      this.skip()
    }
  }
  
  /**
   * 获取当前状态
   */
  get status() {
    return {
      isPlaying: this.isPlaying,
      canSkip: this.canSkip,
      currentResource: this.currentResource,
      watchedDuration: this.playStartTime ? Date.now() - this.playStartTime : 0,
    }
  }
  
  /**
   * 销毁播放器
   */
  destroy(): void {
    this.stop()
    this.videoRef.value = null
    this.imageRef.value = null
  }
}

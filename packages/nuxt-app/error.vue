<template>
  <div class="error-page">
    <div class="error-background">
      <div class="floating-shapes">
        <div class="shape shape-1" />
        <div class="shape shape-2" />
        <div class="shape shape-3" />
        <div class="shape shape-4" />
      </div>
    </div>

    <div class="error-container">
      <div class="error-content">
        <!-- Error Icon -->
        <div class="error-icon">
          <Icon
            v-if="is404"
            name="material-symbols:search-off"
            size="120"
            class="main-icon"
          />
          <Icon
            v-else-if="is500"
            name="material-symbols:error-outline"
            size="120"
            class="main-icon"
          />
          <Icon
            v-else
            name="material-symbols:warning-outline"
            size="120"
            class="main-icon"
          />
        </div>

        <!-- Error Content -->
        <div class="error-info">
          <h1 class="error-title">{{ errorTitle }}</h1>
          <p class="error-message">{{ errorMessage }}</p>

          <!-- Error Details (Dev Mode) -->
          <details v-if="isDev && error.stack" class="error-details">
            <summary>Technical Details</summary>
            <pre class="error-stack">{{ error.stack }}</pre>
          </details>
        </div>

        <!-- Action Buttons -->
        <div class="error-actions">
          <button class="btn btn-primary" @click="goHome">
            <Icon name="material-symbols:home" size="20" />
            Go Home
          </button>

          <button class="btn btn-secondary" @click="goBack">
            <Icon name="material-symbols:arrow-back" size="20" />
            Go Back
          </button>

          <button class="btn btn-tertiary" @click="reload">
            <Icon name="material-symbols:refresh" size="20" />
            Reload
          </button>
        </div>

        <!-- Help Section -->
        <div class="error-help">
          <p class="help-text">
            If this problem persists, please
            <a :href="`mailto:${brand.supportEmail}`" class="help-link"
              >contact support</a
            >
            or try these suggestions:
          </p>

          <ul class="help-suggestions">
            <li v-if="is404">Check the URL spelling and try again</li>
            <li v-if="is404"
              >Use the search function to find what you're looking for</li
            >
            <li v-if="is500">Clear your browser cache and reload</li>
            <li v-if="is500">Check your internet connection</li>
            <li>Try refreshing the page in a few moments</li>
          </ul>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import type { NuxtError } from '#app'

// Props
interface Props {
  error: NuxtError
}

const props = defineProps<Props>()

// 使用品牌配置
const { brandingConfig } = useBranding()
const brand = brandingConfig.value

// 环境检测
const isDev = import.meta.dev

// 错误类型检测
const is404 = computed(() => props.error.statusCode === 404)
const is500 = computed(() => props.error.statusCode === 500)
const isNetworkError = computed(
  () => props.error.statusCode === 0 || props.error.statusCode === -1,
)

// 错误信息
const errorTitle = computed(() => {
  if (is404.value) return 'Page Not Found'
  if (is500.value) return 'Server Error'
  if (isNetworkError.value) return 'Connection Error'
  return 'Something Went Wrong'
})

const errorMessage = computed(() => {
  if (is404.value) {
    return "Sorry, the page you're looking for doesn't exist or has been moved. Let's get you back on track!"
  }
  if (is500.value) {
    return "We're experiencing technical difficulties. Our team has been notified and is working to fix this issue."
  }
  if (isNetworkError.value) {
    return 'Unable to connect to our servers. Please check your internet connection and try again.'
  }
  return (
    props.error.statusMessage ||
    'An unexpected error occurred. Please try again.'
  )
})

// SEO Meta
useHead({
  title: `${errorTitle.value} - ${brand.websiteTitle}`,
  meta: [
    { name: 'robots', content: 'noindex, nofollow' },
    { name: 'description', content: errorMessage.value },
  ],
})

// 页面操作
const goHome = () => {
  navigateTo('/')
}

const goBack = () => {
  if (typeof window !== 'undefined' && window.history.length > 1) {
    window.history.back()
  } else {
    navigateTo('/')
  }
}

const reload = () => {
  if (typeof window !== 'undefined') {
    window.location.reload()
  }
}

// 错误追踪 (生产环境)
if (!isDev) {
  console.error('Error page rendered:', {
    statusCode: props.error.statusCode,
    statusMessage: props.error.statusMessage,
    url: typeof window !== 'undefined' ? window.location.href : 'SSR',
    timestamp: new Date().toISOString(),
  })
}
</script>

<style lang="less" scoped>
.error-page {
  min-height: calc(var(--vh, 1vh) * 100);
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
  background: linear-gradient(
    135deg,
    var(--bg-primary) 0%,
    var(--bg-secondary) 100%
  );
}

.error-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;

  .floating-shapes {
    position: absolute;
    width: 100%;
    height: 100%;

    .shape {
      position: absolute;
      opacity: 0.1;
      background: var(--accent-color);
      animation: float 6s ease-in-out infinite;

      &.shape-1 {
        width: 120px;
        height: 120px;
        border-radius: 50%;
        top: 20%;
        left: 10%;
        animation-delay: 0s;
      }

      &.shape-2 {
        width: 80px;
        height: 80px;
        border-radius: 20px;
        top: 60%;
        right: 15%;
        animation-delay: 2s;
      }

      &.shape-3 {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        bottom: 30%;
        left: 20%;
        animation-delay: 4s;
      }

      &.shape-4 {
        width: 100px;
        height: 100px;
        border-radius: 30px;
        top: 10%;
        right: 30%;
        animation-delay: 1s;
      }
    }
  }
}

.error-container {
  position: relative;
  z-index: 2;
  max-width: 600px;
  width: 90%;
  padding: 0 20px;
}

.error-content {
  background: var(--bg-secondary);
  border-radius: 24px;
  padding: 60px 40px;
  text-align: center;
  box-shadow: 0 20px 60px var(--shadow-color);
  border: 1px solid var(--border-color);
  backdrop-filter: blur(10px);
}

.error-icon {
  margin-bottom: 30px;

  .main-icon {
    color: var(--accent-color);
    filter: drop-shadow(
      0 4px 12px rgba(var(--accent-color-rgb, 74, 144, 226), 0.3)
    );
    animation: pulse 2s infinite;
  }
}

.error-info {
  margin-bottom: 40px;

  .error-title {
    font-size: 32px;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: 16px;
    line-height: 1.2;
  }

  .error-message {
    font-size: 16px;
    color: var(--text-secondary);
    line-height: 1.6;
    margin-bottom: 24px;
  }
}

.error-details {
  background: var(--bg-tertiary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  padding: 20px;
  margin-top: 20px;
  text-align: left;

  summary {
    cursor: pointer;
    font-weight: 600;
    color: var(--text-primary);
    margin-bottom: 10px;

    &:hover {
      color: var(--accent-color);
    }
  }

  .error-stack {
    font-size: 12px;
    color: var(--text-tertiary);
    white-space: pre-wrap;
    word-break: break-all;
    max-height: 200px;
    overflow-y: auto;
    background: var(--bg-primary);
    padding: 12px;
    border-radius: 6px;
    margin-top: 10px;
  }
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  margin-bottom: 40px;
  flex-wrap: wrap;

  .btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 14px 24px;
    border-radius: 12px;
    font-size: 14px;
    font-weight: 600;
    border: none;
    cursor: pointer;
    transition: all 0.2s ease;
    text-decoration: none;
    min-width: 120px;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }

    &.btn-primary {
      background: var(--accent-color);
      color: var(--text-on-accent);

      &:hover {
        background: var(--accent-hover);
      }
    }

    &.btn-secondary {
      background: var(--bg-hover);
      color: var(--text-primary);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--bg-tertiary);
        border-color: var(--accent-color);
      }
    }

    &.btn-tertiary {
      background: transparent;
      color: var(--text-secondary);
      border: 1px solid var(--border-color);

      &:hover {
        background: var(--bg-hover);
        color: var(--text-primary);
      }
    }
  }
}

.error-help {
  border-top: 1px solid var(--border-color);
  padding-top: 30px;
  text-align: left;

  .help-text {
    font-size: 14px;
    color: var(--text-secondary);
    margin-bottom: 16px;

    .help-link {
      color: var(--accent-color);
      text-decoration: none;

      &:hover {
        text-decoration: underline;
      }
    }
  }

  .help-suggestions {
    list-style: none;
    padding: 0;
    margin: 0;

    li {
      position: relative;
      padding-left: 20px;
      margin-bottom: 8px;
      font-size: 14px;
      color: var(--text-tertiary);

      &:before {
        content: '•';
        position: absolute;
        left: 0;
        color: var(--accent-color);
        font-weight: bold;
      }
    }
  }
}

// Mobile Responsive
@media (max-width: 768px) {
  .error-content {
    padding: 40px 24px;

    .error-title {
      font-size: 28px;
    }

    .error-message {
      font-size: 15px;
    }

    .error-icon .main-icon {
      width: 80px;
      height: 80px;
    }
  }

  .error-actions {
    flex-direction: column;

    .btn {
      width: 100%;
      justify-content: center;
    }
  }
}

// Animations
@keyframes float {
  0%,
  100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes pulse {
  0%,
  100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}
</style>

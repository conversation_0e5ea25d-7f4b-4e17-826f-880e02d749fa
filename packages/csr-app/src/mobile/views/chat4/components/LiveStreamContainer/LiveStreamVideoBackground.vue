<template>
  <div class="live-stream-video-background">
    <!-- 主视频 -->
    <video
      ref="primaryVideoRef"
      class="seamless-video primary"
      :class="{
        active:
          mediaSystem?.stateRefs.media.value.liveBackground.currentResource
            ?.type === 'primary',
      }"
      autoplay
      :muted="props.isMuted"
      playsinline
      webkit-playsinline="true"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      preload="auto"
    />

    <!-- 副视频 -->
    <video
      ref="secondaryVideoRef"
      class="seamless-video secondary"
      :class="{
        active:
          mediaSystem?.stateRefs.media.value.liveBackground.currentResource
            ?.type === 'secondary',
      }"
      autoplay
      :muted="props.isMuted"
      playsinline
      webkit-playsinline="true"
      x5-playsinline="true"
      x5-video-player-type="h5"
      x5-video-player-fullscreen="true"
      preload="auto"
    />

    <!-- 前景媒体容器 -->
    <div class="foreground-media-container" v-if="showForegroundMedia">
      <!-- 前景视频 -->
      <video
        ref="foregroundVideoRef"
        class="foreground-video"
        :class="{ active: foregroundMediaType === 'video' }"
        autoplay
        :muted="props.isMuted"
        playsinline
        webkit-playsinline="true"
        x5-playsinline="true"
        x5-video-player-type="h5"
        x5-video-player-fullscreen="true"
        preload="auto"
        @click="handleForegroundClick"
      />

      <!-- 前景图片 -->
      <img
        ref="foregroundImageRef"
        class="foreground-image"
        :class="{ active: foregroundMediaType === 'image' }"
        @click="handleForegroundClick"
      />

      <!-- 跳过按钮 -->
      <div
        v-if="canSkipForeground"
        class="skip-button"
        @click="handleSkipForeground"
      >
        <span>Skip</span>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, nextTick, onBeforeUnmount, computed } from 'vue'
import { UnifiedMediaSystem } from '@/services/media-management'
import type { MediaResource } from '@/services/media-management/types'
import { MediaType } from '@/services/media-management/types'

interface Props {
  videoUrl?: string
  isMuted?: boolean
  videoGroup?: MediaResource[] // 新增：支持视频组
}

const props = withDefaults(defineProps<Props>(), {
  videoUrl: '',
  isMuted: true,
  videoGroup: () => [],
})

const emit = defineEmits<{
  'video-loaded': []
  'video-error': [error: string]
  'audio-prompt-needed': []
  'play-prompt-needed': []
  'user-interaction-required': []
  'foreground-media-start': [resource: MediaResource]
  'foreground-media-end': [resource: MediaResource]
}>()

// 视频元素引用
const primaryVideoRef = ref<HTMLVideoElement>()
const secondaryVideoRef = ref<HTMLVideoElement>()
const foregroundVideoRef = ref<HTMLVideoElement>()
const foregroundImageRef = ref<HTMLImageElement>()

// 媒体管理系统
const mediaSystem = ref<UnifiedMediaSystem | null>(null)

// 前景媒体状态
const showForegroundMedia = computed(() => {
  return mediaSystem.value?.currentState.foreground.isPlaying || false
})

const foregroundMediaType = computed(() => {
  const currentResource =
    mediaSystem.value?.currentState.foreground.currentResource
  if (!currentResource) return null

  return currentResource.type === MediaType.FOREGROUND_VIDEO ? 'video' : 'image'
})

const canSkipForeground = computed(() => {
  if (!mediaSystem.value) return false
  return mediaSystem.value.currentState.foreground.canSkip
})

// 自动播放状态
const autoplayState = ref({
  canAutoplay: false,
  needsUserInteraction: false,
  hasTriedAutoplay: false,
})

/**
 * 初始化媒体管理系统
 */
function initializeMediaSystem() {
  if (mediaSystem.value) return

  mediaSystem.value = new UnifiedMediaSystem(
    {
      debugMode: true,
      audioConfig: {
        ttsVolume: 1.0,
        bgmVolume: 0.8,
        videoVolume: 1.0,
        fadeInDuration: 500,
        fadeOutDuration: 300,
        duckingLevel: 0.3,
      },
    },
    {
      onForegroundStart: (resource) => {
        emit('foreground-media-start', resource)
      },
      onForegroundEnd: (resource) => {
        emit('foreground-media-end', resource)
      },
      onError: (error) => {
        console.error('Media system error:', error)
        emit('video-error', error.message)
      },
    },
  )
}

/**
 * 设置媒体元素到媒体管理系统
 */
function setupMediaElements() {
  if (!mediaSystem.value) return
  if (!primaryVideoRef.value || !secondaryVideoRef.value) return
  if (!foregroundVideoRef.value || !foregroundImageRef.value) return

  mediaSystem.value.setMediaElements({
    primaryVideo: primaryVideoRef.value,
    secondaryVideo: secondaryVideoRef.value,
    foregroundVideo: foregroundVideoRef.value,
    foregroundImage: foregroundImageRef.value,
  })

  console.log('✅ Media elements set up successfully')
}

/**
 * 开始直播背景播放
 */
async function startLiveBackground() {
  if (!mediaSystem.value) return

  // 如果有视频组，使用视频组
  if (props.videoGroup && props.videoGroup.length > 0) {
    try {
      await mediaSystem.value.startLiveBackground(props.videoGroup)
      emit('video-loaded')
    } catch (error) {
      console.error('Failed to start live background with video group:', error)
      emit(
        'video-error',
        error instanceof Error ? error.message : 'Unknown error',
      )
    }
    return
  }

  // 如果有单个视频URL，转换为视频组格式
  if (props.videoUrl) {
    const videoResource: MediaResource = {
      id: `video_${Date.now()}`,
      type: MediaType.LIVE_BACKGROUND,
      url: props.videoUrl,
      priority: 2, // NORMAL priority
    }

    try {
      await mediaSystem.value.startLiveBackground([videoResource])
      emit('video-loaded')
    } catch (error) {
      console.error('Failed to start live background with single video:', error)
      emit(
        'video-error',
        error instanceof Error ? error.message : 'Unknown error',
      )
    }
  }
}

/**
 * 智能自动播放检测
 */
async function trySmartAutoplay(videoElement: HTMLVideoElement) {
  if (autoplayState.value.hasTriedAutoplay) return

  autoplayState.value.hasTriedAutoplay = true

  try {
    // 1. 首先尝试有声自动播放
    videoElement.muted = false
    await videoElement.play()
    autoplayState.value.canAutoplay = true
    console.log('✅ 有声自动播放成功')
    return true
  } catch (error) {
    console.log('❌ 有声自动播放失败，尝试静音播放')

    try {
      // 2. 尝试静音自动播放
      videoElement.muted = true
      await videoElement.play()
      autoplayState.value.canAutoplay = true
      console.log('✅ 静音自动播放成功')

      // 显示音频提示，让用户主动开启声音
      showAudioPrompt()
      return true
    } catch (mutedError) {
      console.log('❌ 静音自动播放也失败，需要用户交互')
      autoplayState.value.needsUserInteraction = true

      // 显示播放按钮或引导用户点击
      showPlayPrompt()
      return false
    }
  }
}

/**
 * 显示音频提示
 */
function showAudioPrompt() {
  // 可以发出事件让父组件显示"点击开启声音"的提示
  emit('audio-prompt-needed')
}

/**
 * 显示播放提示
 */
function showPlayPrompt() {
  // 发出事件让父组件显示播放按钮
  emit('play-prompt-needed')
}

/**
 * 处理前景媒体点击
 */
function handleForegroundClick() {
  // 前景媒体点击时的处理逻辑
  console.log('Foreground media clicked')
}

/**
 * 处理跳过前景媒体
 */
function handleSkipForeground() {
  if (mediaSystem.value) {
    mediaSystem.value.skipForeground()
  }
}

// 监听props变化
watch(
  () => props.videoUrl,
  async (newUrl) => {
    if (newUrl && mediaSystem.value) {
      await startLiveBackground()
    }
  },
  { immediate: false },
)

watch(
  () => props.videoGroup,
  async (newGroup) => {
    if (newGroup && newGroup.length > 0 && mediaSystem.value) {
      await startLiveBackground()
    }
  },
  { immediate: false },
)

// 组件挂载时初始化
onMounted(async () => {
  await nextTick()

  // 初始化媒体管理系统
  initializeMediaSystem()

  // 等待DOM更新
  await nextTick()

  // 设置媒体元素
  setupMediaElements()

  // 开始直播背景播放
  if (props.videoUrl || (props.videoGroup && props.videoGroup.length > 0)) {
    await startLiveBackground()
  }
})

// 组件卸载时清理
onBeforeUnmount(() => {
  if (mediaSystem.value) {
    mediaSystem.value.destroy()
    mediaSystem.value = null
  }
})

// 用户交互处理方法
const handleUserInteraction = async () => {
  if (!autoplayState.value.needsUserInteraction) return

  const primaryVideo = primaryVideoRef.value
  if (!primaryVideo) return

  try {
    await primaryVideo.play()
    autoplayState.value.needsUserInteraction = false
    autoplayState.value.canAutoplay = true
    console.log('✅ 用户交互后播放成功')
  } catch (error) {
    console.error('❌ 用户交互后播放仍然失败:', error)
  }
}

// 开启声音方法
const enableAudio = () => {
  const primaryVideo = primaryVideoRef.value
  if (primaryVideo) {
    primaryVideo.muted = false
    console.log('✅ 声音已开启')
  }
}

// 暴露方法给父组件
defineExpose({
  handleUserInteraction,
  enableAudio,
  autoplayState,
  mediaSystem,
  playForegroundVideo: (resource: MediaResource, config?: any) => {
    return mediaSystem.value?.playForegroundVideo(resource, config)
  },
  showForegroundImage: (resource: MediaResource, config?: any) => {
    return mediaSystem.value?.showForegroundImage(resource, config)
  },
})
</script>

<style scoped>
.live-stream-video-background {
  position: relative;
  width: 100%;
  height: 100%;
  background: #000;
  overflow: hidden;
  /* 强制禁用任何继承的过渡效果 */
  transition: none !important;
  transform: none !important;
  opacity: 1 !important;
}

.seamless-video {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  /* 强制移除任何过渡动画 */
  transition: none !important;
  animation: none !important;
  /* 硬件加速 */
  transform: translateZ(0) !important;
  will-change: auto !important;
  /* 强制显示，避免空白 */
  opacity: 1 !important;
  visibility: visible !important;
  /* 确保没有外边距和内边距 */
  margin: 0 !important;
  padding: 0 !important;
  /* 防止任何滤镜效果 */
  filter: none !important;
  backdrop-filter: none !important;
}

/* 主视频默认在上层 */
.seamless-video.primary {
  z-index: 2 !important;
}

.seamless-video.secondary {
  z-index: 1 !important;
}

/* 活跃视频显示 */
.seamless-video.active {
  z-index: 100 !important;
  opacity: 1 !important;
  visibility: visible !important;
  display: block !important;
}

/* 非活跃视频隐藏 */
.seamless-video:not(.active) {
  z-index: 1 !important;
  opacity: 0 !important;
  visibility: hidden !important;
  display: block !important;
}

/* 前景媒体容器 */
.foreground-media-container {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 200;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.foreground-video,
.foreground-image {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  opacity: 0;
  transition: opacity 0.3s ease;
}

.foreground-video.active,
.foreground-image.active {
  opacity: 1;
}

.skip-button {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 8px 16px;
  border-radius: 20px;
  cursor: pointer;
  font-size: 14px;
  z-index: 210;
  transition: background 0.2s ease;
}

.skip-button:hover {
  background: rgba(0, 0, 0, 0.9);
}
</style>

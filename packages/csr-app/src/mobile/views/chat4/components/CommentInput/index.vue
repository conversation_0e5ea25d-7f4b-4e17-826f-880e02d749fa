<template>
  <div v-if="show" class="comment-input-overlay" @click="handleOverlayClick">
    <div class="comment-input-container" @click.stop>
      <!-- 头部 -->
      <div class="comment-header">
        <button class="cancel-button" @click="handleCancel">Cancel</button>
        <span class="title">Add Comment</span>
        <button
          class="send-button"
          :class="{ active: canSend }"
          :disabled="!canSend"
          @click="handleSend"
        >
          Send
        </button>
      </div>

      <!-- 输入区域 -->
      <div class="comment-body">
        <div class="input-wrapper">
          <textarea
            ref="textareaRef"
            v-model="commentText"
            class="comment-textarea"
            placeholder="Write a comment..."
            :maxlength="maxLength"
            @input="handleInput"
            @focus="handleFocus"
          />
          <div class="char-count">{{ commentText.length }}/{{ maxLength }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'

interface Props {
  show: boolean
  maxLength?: number
  placeholder?: string
}

interface Emits {
  (e: 'update:show', value: boolean): void
  (e: 'submit', comment: string): void
  (e: 'cancel'): void
}

const props = withDefaults(defineProps<Props>(), {
  maxLength: 200,
  placeholder: 'Write a comment...',
})

const emit = defineEmits<Emits>()

// 状态
const commentText = ref('')
const textareaRef = ref<HTMLTextAreaElement>()

// 计算属性
const canSend = computed(() => {
  return (
    commentText.value.trim().length > 0 &&
    commentText.value.length <= props.maxLength
  )
})

// 方法
const handleInput = () => {
  // 自动调整textarea高度
  if (textareaRef.value) {
    textareaRef.value.style.height = 'auto'
    textareaRef.value.style.height = textareaRef.value.scrollHeight + 'px'
  }
}

const handleFocus = () => {
  // 确保在移动端键盘弹出时，输入框可见
  if (textareaRef.value) {
    // 使用requestAnimationFrame确保DOM更新完成后再滚动
    requestAnimationFrame(() => {
      setTimeout(() => {
        // 使用更温和的滚动方式
        textareaRef.value?.scrollIntoView({
          behavior: 'smooth',
          block: 'nearest',
          inline: 'nearest',
        })
      }, 150) // 减少延迟时间
    })
  }
}

const handleSend = () => {
  if (!canSend.value) return

  const comment = commentText.value.trim()
  emit('submit', comment)
  handleClose()
}

const handleCancel = () => {
  emit('cancel')
  handleClose()
}

const handleOverlayClick = () => {
  handleCancel()
}

const handleClose = () => {
  commentText.value = ''
  emit('update:show', false)
}

// 监听显示状态
watch(
  () => props.show,
  (newShow) => {
    if (newShow) {
      // 显示时自动聚焦
      nextTick(() => {
        textareaRef.value?.focus()
      })
    }
  },
)
</script>

<style scoped>
.comment-input-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: flex-end;
  z-index: 10000;
  animation: fadeIn 0.2s ease-out;
  /* 防止移动端键盘弹出时的抖动 */
  height: calc(var(--vh, 1vh) * 100);
  height: 100dvh; /* 使用动态视口高度 */
}

.comment-input-container {
  width: 100%;
  background: white;
  border-radius: 16px 16px 0 0;
  max-height: calc(var(--vh, 1vh) * 80);
  max-height: 80dvh; /* 使用动态视口高度 */
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
  /* 防止键盘弹出时的布局抖动 */
  transform: translateZ(0);
  backface-visibility: hidden;
}

.comment-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
  flex-shrink: 0;
}

.cancel-button {
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  padding: 0;
  cursor: pointer;
}

.title {
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.send-button {
  background: none;
  border: none;
  color: #999;
  font-size: 16px;
  padding: 0;
  cursor: pointer;
  transition: color 0.2s ease;
}

.send-button.active {
  color: #007aff;
}

.send-button:disabled {
  cursor: not-allowed;
}

.comment-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.input-wrapper {
  position: relative;
}

.comment-textarea {
  width: 100%;
  min-height: 80px;
  max-height: 200px;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  font-size: 16px;
  line-height: 1.4;
  resize: none;
  outline: none;
  background: #f8f8f8;
  color: #333; /* 设置文字颜色为深灰色 */
  transition: border-color 0.2s ease;
  box-sizing: border-box;
}

.comment-textarea:focus {
  border-color: #007aff;
  background: white;
  color: #333; /* 确保聚焦时文字颜色也是深色 */
}

.comment-textarea::placeholder {
  color: #999;
}

.char-count {
  position: absolute;
  bottom: 8px;
  right: 12px;
  font-size: 12px;
  color: #999;
  background: rgba(255, 255, 255, 0.9);
  padding: 2px 4px;
  border-radius: 4px;
}

/* 动画 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

/* 移动端适配 */
@media (max-height: 600px) {
  .comment-input-container {
    max-height: 90vh;
    max-height: 90dvh;
  }

  .comment-textarea {
    min-height: 60px;
    max-height: 120px;
  }
}

/* 防止iOS Safari的缩放和抖动 */
@media screen and (max-width: 768px) {
  .comment-textarea {
    font-size: 16px; /* 防止iOS自动缩放 */
    /* 防止键盘弹出时的抖动 */
    transform: translateZ(0);
    -webkit-transform: translateZ(0);
  }

  .comment-input-overlay {
    /* 在移动端使用固定高度，防止键盘影响 */
    height: calc(var(--vh, 1vh) * 100);
    height: 100dvh;
    /* 确保层级和渲染优化 */
    will-change: transform;
    contain: layout style paint;
  }

  .comment-input-container {
    /* 优化渲染性能 */
    will-change: transform;
    contain: layout style paint;
  }
}
</style>

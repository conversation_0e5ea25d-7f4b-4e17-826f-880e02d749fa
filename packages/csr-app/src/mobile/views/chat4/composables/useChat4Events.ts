import { useRouter } from 'vue-router'
import { reportEvent } from '@/utils/report'
import { ReportEvent } from '@/interface'
import { useSceneEvents } from '@/composables/useSceneEvents'
import { useSmartNavigation } from '@/composables/useSmartNavigation'
import { SceneType, type SceneCustomEventDetail } from '@/types/chat4-scene'
import { Chat4Action } from '@/composables/useChat4StateMachine'

import type { Present } from '@/api/chat-multivariate'
import { buyHeartValue } from '@/api/game'
import { useRechargeStore } from '@/store/recharge'
import { Message } from '@/mobile/components/Message'
import { handleSceneNavigation } from '../composables/useSceneUnlock'
import { useAudioManager } from '@/mobile/composables/useAudioManager'

export function useChat4Events(
  props: { characterId?: string },
  state: any,
  refs?: any,
) {
  const router = useRouter()
  const rechargeStore = useRechargeStore()

  // 从 refs 中获取 Chat4 Store
  const chat4Store = refs?.chat4Store

  // 设置发送消息的回调函数
  if (chat4Store) {
    chat4Store.setSendMessageCallback(async (content: string) => {
      console.log('Sending queued message to server:', content)
      try {
        await state.chatEventsStore.sendMessage(
          content,
          null,
          'text',
          false,
          null,
          800,
          false,
          {},
          state.currentScene.value,
        )
        console.log('Queued message sent successfully')
      } catch (error) {
        console.error('Failed to send queued message:', error)
      }
    })
  }

  // 场景事件监听器
  const sceneEvents = useSceneEvents({
    // 好友请求事件处理器
    onFriendRequest: (detail: SceneCustomEventDetail) => {
      console.log('Friend request received in Chat4:', detail)

      // 检查当前是否在Living场景
      if (detail.params?.sceneType === SceneType.LIVING) {
        // 先显示好友请求消息，而不是直接显示弹窗
        state.showFriendRequestMessage.value = true
        console.log('Showing friend request message for Living scene')
      }
    },

    // 聊天场景变化处理器
    onChatSceneChange: (detail: SceneCustomEventDetail) => {
      console.log('Chat scene changed in Chat4:', detail)
      // 可以在这里添加聊天场景特有的处理逻辑
    },

    // 通用场景事件处理器
    onSceneEvent: (eventName: string, detail: SceneCustomEventDetail) => {
      console.log(`Scene event in Chat4: ${eventName}`, detail)
      // 弹幕事件现在由 useChat4Messages 直接处理
    },
  })

  // 返回首页
  const handleBackHome = () => {
    // 使用智能导航，在 iframe 环境中通过 postMessage 返回主应用，否则使用正常路由
    const { smartGoHome } = useSmartNavigation()
    smartGoHome()
  }

  // 容器点击处理
  const handleContainerClick = (event: MouseEvent) => {
    console.log('Container clicked:', event)
  }

  // 覆盖层按钮点击处理
  const handleOverlayButton = (buttonType?: string) => {
    console.log('Overlay button clicked:', buttonType || 'unknown')
    // 处理覆盖层按钮点击逻辑
    if (state.chatUIStore.overlay?.button) {
      state.chatUIStore.handleOverlayButtonClick()
    }
  }

  // 重新开始处理
  const handleRestart = async () => {
    console.log('Chat4 Restart requested - performing complete reset')

    // 上报重新开始事件
    reportEvent(ReportEvent.GameEndClickRestart, {
      storyId: state.storyStore.currentStory?.id,
      actorId: props.characterId || state.route.params.actorId,
    })

    // 1. 重置chat4Store状态（如果存在）
    if (chat4Store) {
      console.log('Resetting chat4Store state')
      chat4Store.resetAllState()
    }

    // 2. 重置状态机到初始状态
    if (state.stateMachine) {
      console.log('Resetting state machine to default')
      state.stateMachine.resetToDefault()
    }

    // 3. 清空聊天相关状态
    console.log('Clearing chat events store')
    state.chatEventsStore.clearChat()

    // 4. 重置当前场景
    state.currentScene.value = null

    // 5. 重新初始化聊天
    const actorId = (state.route.params.actorId as string) || props.characterId
    const storyId = state.route.params.storyId as string
    if (actorId && storyId) {
      console.log('Reinitializing chat for actor:', actorId, 'story:', storyId)

      // 确保故事和角色信息已加载
      if (
        !state.storyStore.currentStory ||
        state.storyStore.currentStory.id !== storyId
      ) {
        console.log('Loading story details for restart')
        await state.storyStore.getStoreDetail(storyId)
      }

      if (
        !state.storyStore.currentActor ||
        state.storyStore.currentActor.id !== actorId
      ) {
        console.log('Loading actor details for restart')
        await state.storyStore.getActorDetail(actorId)
      }

      state.chatResourcesStore.isFirstVideo = false
      await state.chatEventsStore.initializeChat(actorId, storyId)
    }

    // 6. 清理restart状态，防止重复执行
    state.chatEventsStore.setShouldRestart(false)
    if (state.chatStore && state.chatStore.setShouldRestart) {
      state.chatStore.setShouldRestart(false)
    }

    console.log('Chat4 restart completed and restart flags cleared')
  }

  // 发送评论处理
  const handleSendComment = async (comment: string) => {
    console.log('Comment sent:', comment)

    // 立即显示用户弹幕，无论是否被缓存
    const userComment = {
      id: `user_comment_${Date.now()}`,
      username: 'You',
      content: comment,
      type: 'self' as const,
    }

    // 使用 Chat4 Store 添加用户弹幕和处理消息
    if (chat4Store) {
      console.log('Adding user danmaku via Chat4 Store:', userComment)
      chat4Store.addLiveComment(userComment)

      // 调试TTS状态
      console.log('TTS State Debug:', {
        isPlaying: chat4Store.ttsState.isPlaying,
        currentMessage: chat4Store.ttsState.currentMessage,
        queueLength: chat4Store.ttsState.queue.length,
        pendingLength: chat4Store.ttsState.pendingDisplayMessages.length,
      })

      // 使用 Chat4 Store 的用户消息处理逻辑
      const shouldSendImmediately = chat4Store.addUserMessage(comment)

      if (!shouldSendImmediately) {
        console.log('Message queued due to TTS playback or auto-send delay')
        return // 消息被缓存，不立即发送
      }
    } else {
      console.warn('Chat4 Store not available, cannot show user danmaku')
    }

    // 使用 chat-events.ts 中的 sendMessage 逻辑发送评论
    console.log('About to send message to server')
    console.log('state:', state)
    console.log('state.chatEventsStore:', state.chatEventsStore)
    console.log('current scene:', state.currentScene?.value)

    if (!state.chatEventsStore) {
      console.error('chatEventsStore is not available!')
      return
    }

    try {
      console.log('Sending message to server:', comment)
      await state.chatEventsStore.sendMessage(
        comment,
        null,
        'text',
        false,
        null,
        800,
        false,
        {},
        state.currentScene.value, // 传入当前场景ID
      )
      console.log('Message sent successfully')
    } catch (error) {
      console.error('Failed to send comment:', error)
    }
  }

  // 打开礼物弹窗
  const handleOpenGiftModal = () => {
    // 上报礼物操作事件
    reportEvent(ReportEvent.GiftAction, {
      userId: state.userStore.userInfo?.uuid,
      story_id: state.route.params.storyId,
      character_id: state.route.params.actorId,
      action_type: 'click',
      current_scene: state.currentScene,
    })

    state.showGiftModal.value = true
  }

  // 礼物发送成功处理
  const handleGiftSent = (gift: Present, quantity: number = 1) => {
    console.log('Gift sent successfully:', gift, 'quantity:', quantity)

    // 计算总价值
    const totalCoins = gift.coins * quantity
    const totalHeartValue = gift.heart_value * quantity

    // 上报送出礼物事件
    reportEvent(ReportEvent.GiftAction, {
      userId: state.userStore.userInfo?.uuid,
      story_id: state.route.params.storyId,
      character_id: state.route.params.actorId,
      action_type: 'send',
      gift_id: gift.id,
      gift_title: gift.title,
      gift_quantity: quantity,
      gift_coins_unit: gift.coins, // 单个礼物价格
      gift_coins_total: totalCoins, // 总价格
      gift_heart_value_unit: gift.heart_value, // 单个礼物好感度
      gift_heart_value_total: totalHeartValue, // 总好感度
      gift_type: determineGiftType(gift), // 礼物类型
      current_scene: state.currentScene.value,
    })

    state.heartClickCount.value++
  }

  // 根据礼物信息判断礼物类型
  const determineGiftType = (gift: Present): string => {
    const title = gift.title.toLowerCase()

    // 根据礼物名称或价格范围判断类型
    if (title.includes('rose') || title.includes('flower')) {
      return 'flower'
    } else if (title.includes('diamond') || title.includes('gem')) {
      return 'luxury'
    } else if (title.includes('heart') || title.includes('love')) {
      return 'romantic'
    } else if (gift.coins >= 1000) {
      return 'premium'
    } else if (gift.coins >= 100) {
      return 'standard'
    } else {
      return 'basic'
    }
  }

  // 心形点击处理
  const handleHeartClick = () => {
    console.log('Heart clicked, triggering heart particles')
    state.heartClickCount.value++

    // 增加观看人数热度
    const increment = Math.floor(Math.random() * 5) + 1
    state.viewerCount.value += increment
  }

  // 倒计时结束处理
  const handleTimeUp = () => {
    console.log('Time is up! Maximum stay time reached.', {
      currentScene: state.currentScene.value,
    })

    // 停止视频组播放
    if (state.chatResourcesStore.isPlayingVideoGroup) {
      console.log('🛑 直播结束，停止视频组播放')
      state.chatResourcesStore.stopVideoGroup()
    }

    // 停止所有音频播放
    const audioManager = useAudioManager()
    console.log('🛑 直播结束，停止所有音频播放')
    audioManager.stopTTS() // 停止TTS播放
    audioManager.stopBgm() // 停止BGM播放

    // 显示直播结束覆盖层
    state.showStreamEndedOverlay.value = true
  }

  // 跳转到聊天室
  const handleGoToChat = async () => {
    console.log('chat4-nav: Switching to chat room')
    console.log(
      'chat4-nav: Current scene before transition:',
      state.currentScene.value,
    )
    console.log('chat4-nav: State machine status:', {
      isTransitioning: state.stateMachine.isTransitioning,
      isLoading: state.stateMachine.isLoading,
    })

    await state.stateMachine.goToChat()

    console.log('chat4-nav: Chat room transition completed')
    console.log(
      'chat4-nav: New scene after transition:',
      state.currentScene.value,
    )
  }

  // 返回直播
  const handleBackToLive = async () => {
    console.log('chat4-nav: Switching back to live stream from phone scene')
    console.log(
      'chat4-nav: Current scene before back to live:',
      state.currentScene.value,
    )
    console.log(
      'chat4-nav: Messages count before back to live:',
      state.chatMessagesStore.messages.length,
    )
    console.log(
      'chat4-nav: Current messages before back to live:',
      state.chatMessagesStore.messages.map((m: any) => ({
        msg_type: m.msg_type,
        sender_type: m.sender_type,
        content_text: m.content?.text?.substring(0, 50) || 'no text',
      })),
    )

    await state.stateMachine.goToLive()

    console.log('chat4-nav: Back to live completed')
    console.log(
      'chat4-nav: New scene after back to live:',
      state.currentScene.value,
    )
    console.log(
      'chat4-nav: Messages count after back to live:',
      state.chatMessagesStore.messages.length,
    )
  }

  // 挂断视频通话
  const handleHangupVideo = async () => {
    console.log('Hanging up video call')
    await state.stateMachine.hangupVideo()
  }

  // 视频通话消息处理
  const handleVideoCallMessage = async (message: string) => {
    console.log('Video call message sent:', message)

    // 上报发送消息事件
    reportEvent(ReportEvent.SendChatMessage, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      messageLength: message.length,
      messageType: 'text',
    })

    // 通过chat-events store发送消息
    try {
      await state.chatEventsStore.sendMessage(
        message,
        null,
        'text',
        false,
        null,
        800,
        false,
        {},
        state.currentScene.value, // 传入当前场景ID
      )
    } catch (error) {
      console.error('Failed to send video call message:', error)
    }
  }

  // 从监控场景返回聊天室
  const handleGoToLivingRoom = async () => {
    console.log('Going to chat room from monitor')
    await state.stateMachine.goToChat()
  }

  // 从地图场景返回
  const handleBackFromMap = async () => {
    await state.stateMachine.goToChat()
  }

  // 地图位置点击处理
  const handleMapLocationClick = async (event: any) => {
    const { location } = event

    // 上报地图位置点击事件
    reportEvent(ReportEvent.LocationAction, {
      userId: state.userStore.userInfo?.uuid,
      story_id: state.route.params.storyId,
      character_id: state.route.params.actorId,
      action_type: 'click',
      location_type: location.id,
      location_name: location.name,
    })

    try {
      // 使用GO_TO_MEETUP动作，明确这是meetup场景跳转
      const locationKey = location.id // 使用服务器返回的key
      await state.stateMachine.transition(
        Chat4Action.GO_TO_MEETUP,
        locationKey as any,
      )
    } catch (error) {
      console.error('Failed to navigate to location:', error)
    }
  }

  // 从约会场景返回地图
  const handleBackFromMeetup = async () => {
    await state.stateMachine.transition(Chat4Action.GO_TO_MAP)
  }

  // 约会场景消息处理
  const handleMeetupMessage = async (message: string) => {
    console.log('Meetup message sent:', message)

    // 上报发送消息事件
    reportEvent(ReportEvent.SendChatMessage, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      messageLength: message.length,
      messageType: 'text',
    })

    // 通过chat-events store发送消息
    try {
      await state.chatEventsStore.sendMessage(
        message,
        null,
        'text',
        false,
        null,
        800,
        false,
        {},
        state.currentScene.value, // 传入当前场景ID
      )
    } catch (error) {
      console.error('Failed to send meetup message:', error)
    }
  }

  // 处理约会场景完成 - 类似chat2的handleTelepathyComplete
  const handleMeetupComplete = async () => {
    if (state.chatResourcesStore.chat4Card?.length) {
      // 如果有选项卡，选项卡会自动显示，不需要额外处理
      return
    }
    // 如果没有选项卡，发送空消息继续对话
    await state.chatEventsStore.sendMessage('', null, 'text', true, null, 0)
  }

  // 从舞蹈场景返回聊天室
  const handleBackFromDancing = async () => {
    console.log('Going back from dancing scene')
    await state.stateMachine.goToChat()
  }

  // 舞蹈场景消息处理
  const handleDancingMessage = async (message: string) => {
    console.log('Dancing message sent:', message)

    // 上报发送消息事件
    reportEvent(ReportEvent.SendChatMessage, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      messageLength: message.length,
      messageType: 'text',
    })

    // 通过chat-events store发送消息
    try {
      await state.chatEventsStore.sendMessage(
        message,
        null,
        'text',
        false,
        null,
        800,
        false,
        {},
        state.currentScene.value, // 传入当前场景ID
      )
    } catch (error) {
      console.error('Failed to send dancing message:', error)
    }
  }

  // 舞蹈场景事件处理
  const handleDancingSceneEvent = (eventName: string, data: any) => {
    console.log('Dancing scene event:', eventName, data)

    // 处理舞蹈动作点击事件
    if (eventName === 'action-click') {
      console.log('Dancing action clicked:', data.actionKey, data.actionLabel)
      // 这里可以添加特定的舞蹈动作处理逻辑
    }
  }

  // 从演唱会场景返回聊天室
  const handleBackFromConcert = async () => {
    console.log('Going back from concert scene')
    await state.stateMachine.goToChat()
  }

  // 从朋友圈场景返回聊天室
  const handleBackFromMoment = async () => {
    console.log('Going back from moment scene')
    await state.stateMachine.goToChat()
  }

  // 从日记场景返回聊天室
  const handleGoBackFromDiary = async () => {
    console.log('Going back from diary scene')
    await state.stateMachine.goToChat()
  }

  // 演唱会场景消息处理
  const handleConcertMessage = async (message: string) => {
    console.log('Concert message sent:', message)

    // 上报发送消息事件
    reportEvent(ReportEvent.SendChatMessage, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      messageContent: message,
      scene: 'concert',
    })

    try {
      // 发送消息到聊天系统
      await state.chatMessagesStore.sendMessage(message)
    } catch (error) {
      console.error('Failed to send concert message:', error)
    }
  }

  // 演唱会弹幕处理
  const handleConcertDanmaku = (
    content: string,
    type: 'normal' | 'special',
  ) => {
    console.log('Concert danmaku sent:', content, type)

    // 上报弹幕发送事件
    reportEvent(ReportEvent.ClickMenu, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      menuType: `concert_danmaku_${type}`,
      extraData: { content },
    })
  }

  // 演唱会场景事件处理
  const handleConcertSceneEvent = (eventName: string, data: any) => {
    console.log('Concert scene event:', eventName, data)

    // 处理弹幕发送事件
    if (eventName === 'danmaku-sent') {
      handleConcertDanmaku(data.content, data.type)
    }

    // 处理其他演唱会相关事件
    if (eventName === 'action-click') {
      console.log('Concert action clicked:', data.actionKey, data.actionLabel)

      // 上报演唱会动作点击事件
      reportEvent(ReportEvent.ClickMenu, {
        userId: state.userStore.userInfo?.uuid,
        actorId: state.storyStore.currentActor?.id,
        storyId: state.storyStore.currentStory?.id,
        menuType: `concert_${data.actionKey}`,
      })
    }
  }

  // 聊天室消息发送
  const handleChatRoomMessage = async (message: string) => {
    console.log('Chat room message sent:', message)

    // 上报发送消息事件
    reportEvent(ReportEvent.SendChatMessage, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      messageLength: message.length,
      messageType: 'text',
    })

    // 通过chat-events store发送消息
    try {
      await state.chatEventsStore.sendMessage(
        message,
        null,
        'text',
        false,
        null,
        800,
        false,
        {},
        state.currentScene.value, // 传入当前场景ID
      )
    } catch (error) {
      console.error('Failed to send chat room message:', error)
    }
  }

  // 聊天输入焦点处理
  const handleChatInputFocus = () => {
    console.log('Chat input focused')
  }

  const handleChatInputBlur = () => {
    console.log('Chat input blurred')
  }

  // 聊天室场景事件处理
  const handleChatRoomSceneEvent = (
    eventName: string,
    detail: SceneCustomEventDetail,
  ) => {
    console.log('Chat room scene event:', eventName, detail)
  }

  // 导航点击处理
  const handleNavClick = async (navType: string) => {
    console.log('Navigation clicked in Chat4:', navType)

    // 上报场景操作事件
    reportEvent(ReportEvent.SceneAction, {
      userId: state.userStore.userInfo?.uuid,
      story_id: state.route.params.storyId,
      character_id: state.route.params.actorId,
      action_type: 'click',
      scene_type: navType,
      current_scene: state.currentScene.value,
    })

    // 使用统一的场景导航处理
    await handleSceneNavigation(navType, state.stateMachine, {
      onTip: () => {
        console.log('Opening gift modal from tip navigation')
        handleOpenGiftModal()
      },
      onUnknown: (sceneId: string) => {
        console.log('Unknown navigation type:', sceneId)
      },
    })
  }

  // 登录成功处理
  const handleLoginSuccess = async () => {
    await state.userStore.getUserInfo()
    state.showAuthDrawer.value = false
  }

  // 注册成功处理
  const handleRegisterSuccess = () => {
    // 上报用户注册事件
    reportEvent(ReportEvent.UserRegistration, {
      userId: state.userStore.userInfo?.uuid,
      story_id: state.route.params.storyId,
      character_id: state.route.params.actorId,
      registration_source: 'chat4',
      timestamp: Date.now(),
    })

    state.showAuthDrawer.value = false
  }

  // 处理返回按钮点击 - 显示离开确认弹窗
  const handleBackClick = () => {
    console.log('Back button clicked, showing leave confirm modal')
    state.showLeaveConfirmModal.value = true
  }

  // 离开确认处理 - 用户确认离开
  const handleLeaveConfirm = () => {
    console.log('User confirmed leave')

    // 设置已确认离开标记，避免路由守卫再次弹出确认框
    state.chatEventsStore.isConfirmedLeave = true

    // 确保停止视频组播放
    if (state.chatResourcesStore.isPlayingVideoGroup) {
      console.log('🛑 用户确认离开，停止视频组播放')
      state.chatResourcesStore.stopVideoGroup()
    }

    state.showLeaveConfirmModal.value = false
    if (state.nextCallback.get()) {
      // 清理聊天相关状态
      state.chatMessagesStore.clearMessages()
      state.chatEventsStore.clearChat()

      // 获取并清空导航回调，避免重复触发
      const callback = state.nextCallback.get()
      state.nextCallback.set(null)

      // 执行导航回调
      callback()

      // 计算并上报聊天时长
      if (document.hidden && state.lastHiddenTime.value > 0) {
        state.totalHiddenTime.value += Date.now() - state.lastHiddenTime.value
      }

      const totalDuration = Date.now() - state.startTime.value
      const visibleDuration = Math.max(
        0,
        Math.floor((totalDuration - state.totalHiddenTime.value) / 1000),
      )

      reportEvent(ReportEvent.ChatDuration, {
        characterId: props.characterId,
        duration: visibleDuration,
        userId: state.userStore.userInfo?.uuid,
        isInChat: !props.characterId,
        actorId: props.characterId || state.route.params.actorId,
      })
    } else {
      // 如果没有导航回调，直接返回首页
      router.push('/')
    }
  }

  // 离开取消处理
  const handleLeaveCancel = () => {
    state.showLeaveConfirmModal.value = false
    state.nextCallback.set(null)
  }

  // 好友请求消息接受处理
  const handleFriendRequestAccept = () => {
    console.log('User accepted friend request from message')
    // 隐藏好友请求消息
    state.showFriendRequestMessage.value = false
    // 标记已接受好友请求
    state.friendRequestAccepted.value = true
    // 显示好友请求弹窗
    state.showFriendRequestModal.value = true
  }

  // 好友请求弹窗处理
  const handleKeepWatching = () => {
    console.log('User chose to keep watching')
  }

  const handleStartChatting = async () => {
    console.log('User chose to start chatting')
    await state.stateMachine.acceptFriendRequest()
  }

  // 充值处理
  const handleAddCredit = (actionType: string) => {
    console.log('Add credit requested from:', actionType)

    if (state.userStore.isGuest) {
      state.showAuthDrawer.value = true
    } else {
      state.rechargeStore.toggleRechargeModal()
    }

    reportEvent(ReportEvent.ClickAddCreditInChat, {
      userId: state.userStore.userInfo?.uuid,
      actorId: props.characterId || state.route.params.actorId,
      storyId: state.storyStore.currentStory?.id,
      actionType,
    })
  }

  // 直播结束覆盖层事件处理
  const handleStreamEndedBack = () => {
    console.log('User clicked back from stream ended overlay')

    // 确保停止视频组播放
    if (state.chatResourcesStore.isPlayingVideoGroup) {
      console.log('🛑 用户离开直播，停止视频组播放')
      state.chatResourcesStore.stopVideoGroup()
    }

    // 确保停止所有音频播放
    const audioManager = useAudioManager()
    console.log('🛑 用户离开直播，停止所有音频播放')
    audioManager.stopTTS() // 停止TTS播放
    audioManager.stopBgm() // 停止BGM播放

    // 隐藏直播结束覆盖层
    state.showStreamEndedOverlay.value = false
    // 返回首页
    state.router.push('/')
  }

  const handleExploreOthers = () => {
    console.log('User chose to explore others')

    // 确保停止视频组播放
    if (state.chatResourcesStore.isPlayingVideoGroup) {
      console.log('🛑 用户选择探索其他，停止视频组播放')
      state.chatResourcesStore.stopVideoGroup()
    }

    // 上报事件
    reportEvent(ReportEvent.ClickMenu, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      menuType: 'explore_others',
    })
    // 返回首页
    router.push('/')
  }

  const handleStreamEndedStartChatting = async () => {
    console.log('User chose to start chatting from stream ended overlay')

    // 确保停止视频组播放
    if (state.chatResourcesStore.isPlayingVideoGroup) {
      console.log('🛑 用户开始聊天，停止视频组播放')
      state.chatResourcesStore.stopVideoGroup()
    }

    // 确保停止所有音频播放
    const audioManager = useAudioManager()
    console.log('🛑 用户开始聊天，停止所有音频播放')
    audioManager.stopTTS() // 停止TTS播放
    audioManager.stopBgm() // 停止BGM播放

    // 隐藏直播结束覆盖层
    state.showStreamEndedOverlay.value = false
    // 上报事件
    reportEvent(ReportEvent.ClickMenu, {
      userId: state.userStore.userInfo?.uuid,
      actorId: state.storyStore.currentActor?.id,
      storyId: state.storyStore.currentStory?.id,
      menuType: 'start_chatting_from_stream_end',
    })

    // 先接受好友请求，解锁聊天按钮
    console.log('Accepting friend request to unlock chat button')
    await state.stateMachine.acceptFriendRequest()

    // 然后跳转到聊天室
    await state.stateMachine.goToChat()
  }

  // Stage组件点击处理
  const handleStageClick = () => {
    console.log('Stage clicked, opening favorability drawer')

    const currentHeartValue =
      state.chatEventsStore.favorabilityState.currentHeartValue
    const currentLevel = state.chatEventsStore.favorabilityState.currentLevel

    // 上报好感度操作事件
    reportEvent(ReportEvent.FavorabilityAction, {
      userId: state.userStore.userInfo?.uuid,
      story_id: state.route.params.storyId,
      character_id: state.route.params.actorId,
      action_type: 'stage_click',
      heart_value: currentHeartValue,
      current_level: currentLevel,
    })

    // 显示好感度抽屉
    state.showFavorabilityDrawer.value = true
  }

  // 好感度抽屉关闭处理
  const handleFavorabilityDrawerClose = () => {
    console.log('Favorability drawer closed')
    state.showFavorabilityDrawer.value = false
  }

  // 好感度抽屉付费处理
  const handleFavorabilityPayment = (coins: number) => {
    console.log('Favorability payment requested:', coins)

    if (!chat4Store) {
      console.error('Chat4Store is not available!')
      return
    }

    // 使用 store 方法设置付费状态
    chat4Store.requestFavorabilityPayment(coins)
    // 关闭好感度抽屉
    state.showFavorabilityDrawer.value = false
  }

  // 付费确认处理
  const handlePaymentConfirm = async () => {
    console.log('Payment confirmed, processing payment...')

    const coins = chat4Store.unlockState.paymentCoins
    const storyId = state.storyStore.currentStory?.id
    const actorId = state.storyStore.currentActor?.id

    if (!storyId || !actorId) {
      console.error('Missing story or actor ID for payment')
      Message.error('Payment failed: Missing required information')
      return
    }

    // 检查用户钻石余额
    const userCoins = state.userStore.userInfo?.coins || 0
    if (userCoins < coins) {
      console.log('Insufficient coins, showing recharge modal')
      chat4Store.closePaymentConfirmModal()
      rechargeStore.showRechargeModal()
      return
    }

    try {
      // 调用购买好感度API
      const response = await buyHeartValue({
        story_id: storyId,
        actor_id: actorId,
        coins: coins,
      })
      if (response.code === '0') {
        Message.success(
          `Successfully purchased heart value for ${coins} diamonds!`,
        )

        // 更新用户钻石余额
        if (response.data?.remaining_coins !== undefined) {
          state.userStore.userInfo.coins = response.data.remaining_coins
        } else {
          // 如果API没有返回剩余金币，手动扣除
          const currentCoins = state.userStore.userInfo?.coins || 0
          state.userStore.userInfo.coins = Math.max(0, currentCoins - coins)
        }

        // 更新好感度值（heart_value是绝对值，不是增量）
        if (response.data?.heart_value !== undefined) {
          const oldHeartValue =
            state.chatEventsStore.favorabilityState.currentHeartValue
          const newHeartValue = response.data.heart_value
          console.log(
            `Updating heart value from favorability payment: ${oldHeartValue} -> ${newHeartValue}`,
          )

          // 更新好感度状态
          state.chatEventsStore.favorabilityState.currentHeartValue =
            newHeartValue

          // 重新计算等级信息和场景解锁状态
          state.chatEventsStore.updateFavorabilityLevelInfo()

          // 强制触发响应式更新
          console.log('Updated favorability state:', {
            currentHeartValue:
              state.chatEventsStore.favorabilityState.currentHeartValue,
            heartValueLeft:
              state.chatEventsStore.favorabilityState.heartValueLeft,
            nextLevel: state.chatEventsStore.favorabilityState.nextLevel,
            nextLevelHeartValue:
              state.chatEventsStore.favorabilityState.nextLevelHeartValue,
          })
        }

        // 上报付费人数事件
        reportEvent(ReportEvent.PayingUser, {
          userId: state.userStore.userInfo?.uuid,
          story_id: state.route.params.storyId,
          character_id: state.route.params.actorId,
          payment_amount: chat4Store.favorabilityPaymentCoins,
          payment_type: 'favorability',
          timestamp: Date.now(),
        })

        // 上报付费成功事件
        reportEvent(ReportEvent.PaymentSuccess, {
          userId: state.userStore.userInfo?.uuid,
          actorId: actorId,
          storyId: storyId,
          menuType: 'heart_value_purchase_success',
          coins: coins,
          heartValueGained: response.data?.heart_value || 0,
        })
      } else {
        console.error('Payment failed:', response.message)
        Message.error(response.message || 'Payment failed')
      }
    } catch (error) {
      console.error('Payment error:', error)
      Message.error('Payment failed. Please try again.')
    } finally {
      // 重置PaymentConfirmModal的loading状态
      if (refs?.paymentConfirmModalRef?.value?.resetLoading) {
        refs.paymentConfirmModalRef.value.resetLoading()
      }

      // 关闭付费确认弹窗
      chat4Store.closePaymentConfirmModal()
    }
  }

  // 处理队列消息发送
  const handleQueuedMessage = async (content: string) => {
    console.log('Handling queued message:', content)

    // 使用现有的发送评论逻辑
    try {
      await state.chatEventsStore.sendMessage(
        content,
        null,
        'text',
        false,
        null,
        800,
        false,
        {},
        state.currentScene.value,
      )
    } catch (error) {
      console.error('Failed to send queued message:', error)
    }
  }

  return {
    sceneEvents,
    handleBackHome,
    handleContainerClick,
    handleOverlayButton,
    handleRestart,
    handleSendComment,
    handleOpenGiftModal,
    handleGiftSent,
    handleHeartClick,
    handleTimeUp,
    handleGoToChat,
    handleBackToLive,
    handleHangupVideo,
    handleVideoCallMessage,
    handleGoToLivingRoom,
    handleBackFromMap,
    handleMapLocationClick,
    handleBackFromMeetup,
    handleMeetupMessage,
    handleMeetupComplete,
    handleBackFromDancing,
    handleDancingMessage,
    handleDancingSceneEvent,
    handleBackFromConcert,
    handleBackFromMoment,
    handleGoBackFromDiary,
    handleConcertMessage,
    handleConcertSceneEvent,
    handleConcertDanmaku,
    handleChatRoomMessage,
    handleChatInputFocus,
    handleChatInputBlur,
    handleChatRoomSceneEvent,
    handleNavClick,
    handleLoginSuccess,
    handleRegisterSuccess,
    handleBackClick,
    handleLeaveConfirm,
    handleLeaveCancel,
    handleFriendRequestAccept,
    handleKeepWatching,
    handleStartChatting,
    handleAddCredit,
    handleStreamEndedBack,
    handleExploreOthers,
    handleStreamEndedStartChatting,
    handleStageClick,
    handleFavorabilityDrawerClose,
    handleFavorabilityPayment,
    handlePaymentConfirm,
    handleQueuedMessage,
  }
}

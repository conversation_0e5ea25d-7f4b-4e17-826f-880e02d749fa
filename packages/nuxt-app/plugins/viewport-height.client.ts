/**
 * 视口高度适配插件
 * 设置 --vh CSS 变量以适配移动端浏览器的视口高度问题
 */

export default defineNuxtPlugin(() => {
  // 确保只在客户端运行
  if (!import.meta.client) return

  // 设置 --vh CSS 变量
  function setVh() {
    // 确保 DOM 可用
    if (typeof window === 'undefined' || !document.documentElement) return

    // 使用 requestAnimationFrame 确保在下一帧计算 vh
    requestAnimationFrame(() => {
      const vh = window.innerHeight * 0.01
      document.documentElement.style.setProperty('--vh', `${vh}px`)
    })
  }

  // 防抖函数优化 resize 事件处理
  function debounce<T extends (...args: any[]) => void>(fn: T, delay: number) {
    let timer: number | null = null
    return function (this: any, ...args: Parameters<T>) {
      if (timer) window.clearTimeout(timer)
      timer = window.setTimeout(() => {
        fn.apply(this, args)
      }, delay)
    }
  }

  // 初始化设置
  setVh()

  // 监听窗口大小变化，使用防抖优化
  const debouncedSetVh = debounce(setVh, 100)
  if (typeof window !== 'undefined') {
    window.addEventListener('resize', debouncedSetVh)

    // 监听页面可见性变化，确保在页面切换回来时重新计算
    document.addEventListener('visibilitychange', () => {
      if (document.visibilityState === 'visible') {
        setVh()
      }
    })

    // 确保在所有资源加载完成后重新计算一次
    window.addEventListener('load', () => {
      setVh()
    })

    // 监听方向变化（移动端）
    window.addEventListener('orientationchange', () => {
      // 延迟执行，等待方向变化完成
      setTimeout(setVh, 100)
    })
  }
})

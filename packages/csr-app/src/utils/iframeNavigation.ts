/**
 * iframe导航工具
 * 用于在微前端环境中与主应用通信
 */

import {
  getDeploymentConfig,
  isInMicroFrontendEnvironment,
} from '@/config/deployment'

// 路由对象类型定义
interface RouteLocationRaw {
  path?: string
  name?: string
  params?: Record<string, any>
  query?: Record<string, any>
  hash?: string
}

/**
 * 检测是否在iframe中运行
 */
export function isInIframe(): boolean {
  if (typeof window === 'undefined') return false
  return window.self !== window.top
}

/**
 * 检测是否在微前端环境中
 */
export function isInMicroFrontend(): boolean {
  return isInMicroFrontendEnvironment()
}

/**
 * 向主应用发送消息
 */
export function sendMessageToParent(type: string, payload?: any) {
  if (!isInMicroFrontend()) {
    console.warn('不在微前端环境中，无法发送消息')
    return false
  }

  const message = {
    type,
    payload,
    timestamp: Date.now(),
    source: 'csr-app',
  }


  // 发送消息到父窗口（主应用）
  const config = getDeploymentConfig()
  window.parent.postMessage(message, config.mainAppUrl)
  return true
}

/**
 * 向主应用发送导航消息
 */
export function sendNavigationMessage(type: string, payload?: any) {
  return sendMessageToParent(type, payload)
}
/**
 * 向主应用发送加载完成消息
 */
export function sendContentReadyMessage() {
  return sendMessageToParent('CONTENT_READY')
}

/**
 * 导航到主应用首页
 */
export function navigateToHome() {
  if (sendNavigationMessage('NAVIGATE_TO_HOME')) {
    return true
  }

  // 降级：如果不在微前端环境中，使用正常路由
  return false
}

/**
 * 导航到故事列表
 */
export function navigateToStories() {
  if (sendNavigationMessage('NAVIGATE_TO_STORIES')) {
    return true
  }

  // 降级：如果不在微前端环境中，使用正常路由
  return false
}

/**
 * 导航到特定故事
 */
export function navigateToStory(storyId: string) {
  if (sendNavigationMessage('NAVIGATE_TO_STORY', { storyId })) {
    return true
  }

  // 降级：如果不在微前端环境中，使用正常路由
  return false
}

/**
 * 返回上一页
 */
export function goBack() {
  if (sendNavigationMessage('GO_BACK')) {
    return true
  }

  // 降级：如果不在微前端环境中，使用正常路由
  return false
}

/**
 * 同步用户认证信息到主应用
 */
export function syncAuthState(authData: {
  token?: string
  refreshToken?: string
  sessionId?: string
  userId?: string
}) {
  return sendMessageToParent('SYNC_AUTH_STATE', authData)
}

/**
 * 同步用户状态到主应用
 */
export function syncUserState(userData: {
  user?: any
  userPreferences?: any
  language?: string
  theme?: string
}) {
  return sendMessageToParent('SYNC_USER_STATE', userData)
}

/**
 * 请求主应用的状态
 */
export function requestParentState() {
  return sendMessageToParent('REQUEST_STATE')
}

/**
 * 通知主应用CSR应用已就绪
 */
export function notifyAppReady() {
  return sendMessageToParent('CSR_APP_READY')
}

/**
 * 通知主应用CSR应用路由已就绪
 */
export function notifyRouteReady() {
  return sendMessageToParent('CSR_ROUTE_READY')
}

/**
 * 报告加载进度到主应用
 */
export function reportLoadingProgress(stage: string, progress: number) {
  return sendMessageToParent('LOADING_PROGRESS', { stage, progress })
}

/**
 * 请求父应用处理支付重定向
 */
export function requestPaymentRedirect(paymentData: {
  provider: string
  redirectUrl?: string
  sessionId?: string
  useStripeSDK?: boolean
  stripePublicKey?: string
}) {
  return sendMessageToParent('PAYMENT_REDIRECT', paymentData)
}

/**
 * 智能导航函数 - 自动判断环境并选择合适的导航方式
 */
export function smartNavigate(path: string, router?: any) {
  // 解析路径并选择合适的导航方法
  if (path === '/' || path === '/home') {
    if (navigateToHome()) return
  } else if (path === '/stories') {
    if (navigateToStories()) return
  } else if (path.startsWith('/story/')) {
    const storyId = path.split('/')[2]
    if (storyId && navigateToStory(storyId)) return
  }

  // 如果微前端导航失败，使用传统路由
  if (router) {
    router.push(path)
  } else {
    console.warn('无法导航到:', path)
  }
}

/**
 * 创建智能路由器包装器
 */
export function createSmartRouter(originalRouter: any) {
  return {
    ...originalRouter,
    push: (to: string | RouteLocationRaw) => {
      const path =
        typeof to === 'string' ? to : (to as RouteLocationRaw).path || '/'

      // 尝试智能导航
      if (typeof path === 'string') {
        smartNavigate(path, originalRouter)
      } else {
        // 复杂路由对象，直接使用原始路由器
        originalRouter.push(to)
      }
    },
    replace: (to: string | RouteLocationRaw) => {
      const path =
        typeof to === 'string' ? to : (to as RouteLocationRaw).path || '/'

      // 尝试智能导航
      if (typeof path === 'string') {
        smartNavigate(path, originalRouter)
      } else {
        // 复杂路由对象，直接使用原始路由器
        originalRouter.replace(to)
      }
    },
  }
}

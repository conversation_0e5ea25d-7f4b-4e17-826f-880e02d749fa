/**
 * 火山引擎统计分析客户端插件
 * 配置火山引擎统计参数
 */

export default defineNuxtPlugin(() => {
  // 只在客户端运行
  if (!import.meta.client) return

  const config = useRuntimeConfig()

  // 火山引擎配置
  const volcConfig = {
    // 从环境变量获取APP ID，开发环境使用测试ID
    app_id:
      process.env.NODE_ENV === 'development'
        ? 20005873
        : config.public.volcAppId || 20005873,
    channel: 'web',
    log: process.env.NODE_ENV === 'development', // 开发环境启用日志
    disable_sdk_monitor: false,
    disable_auto_pv: true, // 禁用自动页面浏览统计，手动控制
  }

  // 等待火山引擎主脚本加载完成并配置
  const configVolcAnalytics = () => {
    if (typeof window !== 'undefined' && (window as any).collectEvent) {
      // 检查是否是真正的collectEvent函数（不是缓存队列）
      if (
        typeof (window as any).collectEvent === 'function' &&
        !(window as any).collectEvent.q
      ) {
        console.log('🌋 火山引擎主脚本已加载')

        // 配置火山引擎
        try {
          ;(window as any).collectEvent('config', volcConfig)
          console.log('🌋 火山引擎配置成功', volcConfig)
          return true
        } catch (error) {
          console.error('🌋 火山引擎配置失败:', error)
          return false
        }
      } else {
        console.log('🌋 火山引擎缓存队列已就绪，等待主脚本加载...')
        return false
      }
    } else {
      console.log('🌋 等待火山引擎脚本加载...')
      return false
    }
  }

  // 轮询检查火山引擎是否加载完成
  let isConfigured = false
  const checkInterval = setInterval(() => {
    if (configVolcAnalytics()) {
      isConfigured = true
      clearInterval(checkInterval)
    }
  }, 200)

  // 5秒后停止检查
  setTimeout(() => {
    clearInterval(checkInterval)
    if (process.env.NODE_ENV === 'development' && !isConfigured) {
      console.warn('🔧 火山引擎加载检查超时，可能需要检查网络连接')
    }
  }, 5000)

  // 开发环境调试信息
  if (process.env.NODE_ENV === 'development') {
    setTimeout(() => {
      console.log('🔧 火山引擎调试信息:')
      console.log('- APP ID:', volcConfig.app_id)
      console.log('- collectEvent 可用:', !!(window as any).collectEvent)
      console.log('- 是否为缓存队列:', !!(window as any).collectEvent?.q)
      console.log('- 配置:', volcConfig)
    }, 3000)
  }
})

// 类型声明
declare global {
  interface Window {
    collectEvent?: (eventType: string, eventData?: any) => void
  }
}

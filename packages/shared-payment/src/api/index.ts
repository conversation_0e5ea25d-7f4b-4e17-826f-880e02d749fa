import type { PriceItem, PaymentResult, ApiResponse } from '../types/index'
import { PaymentProviderFactory } from '../providers/index'
import { getProjectConfig } from '../config/project'

/**
 * 支付API封装类
 */
export class PaymentAPI {
  private apiBase: string
  private authTokenGetter?: () => string | null
  private debugPaymentProvider?: string
  private stripePublicKey?: string

  constructor(
    apiBase: string,
    authTokenGetter?: () => string | null,
    options?: { debugPaymentProvider?: string; stripePublicKey?: string },
  ) {
    this.apiBase = apiBase
    this.authTokenGetter = authTokenGetter
    this.debugPaymentProvider = options?.debugPaymentProvider
    this.stripePublicKey = options?.stripePublicKey
  }

  /**
   * 获取价格列表
   */
  async getPriceList(): Promise<PriceItem[]> {
    const response = await this.request<PriceItem[]>('/api/v1/price.list', {
      method: 'GET',
    })

    return response.data || []
  }

  /**
   * 创建支付订单
   */
  async createPayment(params: {
    priceId: string
    successUrl?: string
    cancelUrl?: string
  }): Promise<PaymentResult> {
    // 使用调试参数获取项目配置
    const projectConfig = this.debugPaymentProvider
      ? this.getProjectConfigWithDebug(this.debugPaymentProvider)
      : getProjectConfig()

    try {
      // 直接使用项目配置创建提供商
      const paymentConfig = {
        provider: projectConfig.paymentProvider,
        publicKey: this.stripePublicKey, // 传递 Stripe 公钥
        apiBase: this.apiBase,
        successUrl: params.successUrl || this.getDefaultSuccessUrl(),
        cancelUrl: params.cancelUrl || this.getDefaultCancelUrl(),
      }

      const provider = PaymentProviderFactory.create(
        paymentConfig,
        this.authTokenGetter,
      )

      if (projectConfig.paymentProvider === 'stripe') {
        // Stripe 需要 successUrl 和 cancelUrl
        return await provider.createPayment({
          priceId: params.priceId,
          successUrl: params.successUrl || this.getDefaultSuccessUrl(),
          cancelUrl: params.cancelUrl || this.getDefaultCancelUrl(),
        })
      } else if (projectConfig.paymentProvider === 'onlypay') {
        // Onlypay 只需要 redirectUrl，使用正确的成功回调URL
        const successUrl =
          params.successUrl ||
          this.getDefaultSuccessUrlForProvider(projectConfig.paymentProvider)
        return await provider.createPayment({
          priceId: params.priceId,
          redirectUrl: successUrl,
        })
      } else {
        throw new Error(
          `Unsupported payment provider: ${projectConfig.paymentProvider}`,
        )
      }
    } catch (error) {
      return {
        success: false,
        provider: projectConfig.paymentProvider,
        error:
          error instanceof Error ? error.message : 'Payment creation failed',
      }
    }
  }

  /**
   * 使用调试参数获取项目配置
   */
  private getProjectConfigWithDebug(debugPaymentProvider: string) {
    const configs = {
      stripe: {
        type: 'reelplay' as const,
        paymentProvider: 'stripe' as const,
        branding: {
          name: 'ReelPlay',
        },
      },
      onlypay: {
        type: 'playshot' as const,
        paymentProvider: 'onlypay' as const,
        branding: {
          name: 'PlayShot',
        },
      },
    }

    return (
      configs[debugPaymentProvider as keyof typeof configs] || configs.stripe
    )
  }

  /**
   * 检测是否在iframe中运行
   */
  private isInIframe(): boolean {
    if (typeof window === 'undefined') return false
    return window.self !== window.top
  }

  /**
   * 向父应用发送支付重定向消息
   */
  private sendPaymentRedirectToParent(paymentData: {
    provider: string
    redirectUrl?: string
    sessionId?: string
    useStripeSDK?: boolean
  }): boolean {
    if (typeof window === 'undefined' || !this.isInIframe()) {
      return false
    }

    try {
      const message = {
        type: 'PAYMENT_REDIRECT',
        payload: {
          ...paymentData,
          stripePublicKey: this.stripePublicKey,
        },
        timestamp: Date.now(),
        source: 'csr-app',
      }

      console.log('📤 CSR应用: 发送支付重定向消息到主应用', message)

      // 尝试获取主应用的origin，如果无法获取则使用通配符
      let targetOrigin = '*'
      try {
        // 尝试从当前页面的referrer获取主应用域名
        if (document.referrer) {
          const referrerUrl = new URL(document.referrer)
          targetOrigin = referrerUrl.origin
        }
      } catch (error) {
        console.warn('无法获取主应用origin，使用通配符')
      }

      window.parent.postMessage(message, targetOrigin)
      return true
    } catch (error) {
      console.error('发送支付重定向消息失败:', error)
      return false
    }
  }

  /**
   * 重定向到支付页面
   */
  async redirectToPayment(paymentResult: PaymentResult): Promise<void> {
    if (!paymentResult.success) {
      throw new Error(paymentResult.error || 'Payment failed')
    }

    if (typeof window === 'undefined') {
      return // SSR 环境不处理重定向
    }

    // 如果在iframe中，尝试通过父应用处理支付
    if (this.isInIframe()) {
      const success = this.sendPaymentRedirectToParent({
        provider: paymentResult.provider,
        redirectUrl: paymentResult.redirectUrl,
        sessionId: paymentResult.sessionId,
        useStripeSDK: paymentResult.useStripeSDK,
      })

      if (success) {
        console.log('✅ 已通知父应用处理支付重定向')
        return
      }

      console.warn('⚠️ 无法通知父应用，降级到iframe内重定向（可能被阻止）')
    }

    // 如果是 Stripe 且需要使用 SDK
    if (
      paymentResult.provider === 'stripe' &&
      paymentResult.useStripeSDK &&
      paymentResult.sessionId
    ) {
      if (!this.stripePublicKey) {
        throw new Error('Stripe public key is required for SDK redirect')
      }

      try {
        // 动态导入 Stripe SDK
        const { loadStripe } = await import('@stripe/stripe-js')
        const stripe = await loadStripe(this.stripePublicKey)

        if (!stripe) {
          throw new Error('Failed to load Stripe SDK')
        }

        // 使用官方 SDK 重定向
        const { error } = await stripe.redirectToCheckout({
          sessionId: paymentResult.sessionId,
        })

        if (error) {
          throw new Error(error.message || 'Stripe redirect failed')
        }
      } catch (error) {
        // 降级到直接 URL 重定向
        window.location.href = `https://checkout.stripe.com/pay/${paymentResult.sessionId}`
      }
    } else if (paymentResult.redirectUrl) {
      // 其他支付方式使用直接重定向
      window.location.href = paymentResult.redirectUrl
    } else {
      throw new Error('No redirect URL or session ID provided')
    }
  }

  /**
   * 创建支付并重定向（一步完成）
   */
  async createAndRedirectToPayment(params: {
    priceId: string
    successUrl?: string
    cancelUrl?: string
  }): Promise<void> {
    const result = await this.createPayment(params)

    if (result.success) {
      await this.redirectToPayment(result)
    } else {
      throw new Error(result.error || 'Payment creation failed')
    }
  }

  /**
   * 通用请求方法
   */
  private async request<T>(
    endpoint: string,
    options: RequestInit = {},
  ): Promise<ApiResponse<T>> {
    const url = `${this.apiBase}${endpoint}`

    const headers: Record<string, string> = {
      'Content-Type': 'application/json',
      ...(options.headers as Record<string, string>),
    }

    // 添加认证头
    const token = this.authTokenGetter?.()
    if (token) {
      headers.Authorization = `Bearer ${token}`
    }

    const response = await fetch(url, {
      ...options,
      headers,
    })

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`)
    }

    return await response.json()
  }

  private getDefaultSuccessUrlForProvider(paymentProvider: string): string {
    if (typeof window !== 'undefined') {
      const baseUrl = `${window.location.origin}/recharge-success`

      // 为 Stripe 支付添加预设参数，标识这是 Stripe 支付成功
      if (paymentProvider === 'stripe') {
        return `${baseUrl}?provider=stripe&session_id={CHECKOUT_SESSION_ID}`
      } else {
        return baseUrl
      }
    }
    return ''
  }

  private getDefaultSuccessUrl(): string {
    if (typeof window !== 'undefined') {
      // 使用调试参数或默认配置
      const projectConfig = this.debugPaymentProvider
        ? this.getProjectConfigWithDebug(this.debugPaymentProvider)
        : getProjectConfig()

      return this.getDefaultSuccessUrlForProvider(projectConfig.paymentProvider)
    }
    return ''
  }

  private getDefaultCancelUrl(): string {
    if (typeof window !== 'undefined') {
      return window.location.href
    }
    return ''
  }
}

/**
 * 创建支付API实例的工厂函数
 */
export function createPaymentAPI(
  apiBase: string,
  authTokenGetter?: () => string | null,
  options?: { debugPaymentProvider?: string; stripePublicKey?: string },
): PaymentAPI {
  return new PaymentAPI(apiBase, authTokenGetter, options)
}

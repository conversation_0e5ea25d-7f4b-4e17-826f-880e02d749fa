import { defineStore } from 'pinia'

interface Actor {
  id: string
  name: string
  avatar_url?: string
  preview_url?: string
  description?: string
  personality?: string
  version?: string // Chat4 模式标识
}

interface Story {
  id: string
  title: string
  description?: string
  cover_url?: string
  preview_url?: string
  category?: string
  tags?: string[]
  actors?: Actor[]
  is_favorite?: boolean
  play_count?: number
  rating?: number
  created_at?: string
  updated_at?: string
}

interface SubCategory {
  id: string
  name: string
  parent_id: string
  level: number
}

interface Category {
  id: string
  name: string
  parent_id: string
  level: number
  subcategories?: SubCategory[]
}

interface StoryState {
  currentStory: Story | null
  currentActor: Actor | null
  stories: Story[]
  loading: boolean
  error: string | null
  storyCategories: Category[]
  currentCategory: Category
  isFavoriteLoading: boolean
  lastFetchParams?: {
    categoryIds: string[]
    sort: string
  }
  // 添加请求状态跟踪
  categoriesLoading: boolean
  storiesLoading: boolean
}

export const useStoryStore = defineStore('story', {
  state: (): StoryState => ({
    currentStory: null,
    currentActor: null,
    stories: [],
    loading: false,
    error: null,
    storyCategories: [],
    currentCategory: {
      id: 'all',
      name: 'All',
      parent_id: '',
      level: 0,
    },
    isFavoriteLoading: false,
    categoriesLoading: false,
    storiesLoading: false,
  }),

  persist: {
    key: 'story-store',
    pick: ['currentStory', 'currentActor'],
  },

  getters: {
    hotStories: (state) =>
      state.stories.filter(
        (story) => story.play_count && story.play_count > 1000,
      ),
    favoriteStories: (state) =>
      state.stories.filter((story) => story.is_favorite),
    currentStoryActors: (state) => state.currentStory?.actors || [],
  },

  actions: {
    setCurrentStory(story: Story) {
      this.currentStory = story
    },

    setCurrentActor(actor: Actor) {
      this.currentActor = actor
    },

    setStories(stories: Story[]) {
      this.stories = stories
    },

    addStory(story: Story) {
      const existingIndex = this.stories.findIndex((s) => s.id === story.id)
      if (existingIndex >= 0) {
        this.stories[existingIndex] = story
      } else {
        this.stories.push(story)
      }
    },

    setLoading(loading: boolean) {
      this.loading = loading
    },

    setError(error: string | null) {
      this.error = error
    },

    setCategories(categories: Category[]) {
      this.storyCategories = categories
    },

    setCurrentCategory(category: Category) {
      this.currentCategory = category
    },

    async toggleFavorite(storyId: string) {
      this.isFavoriteLoading = true
      try {
        const story = this.stories.find((s) => s.id === storyId)
        if (story) {
          story.is_favorite = !story.is_favorite
          // 这里应该调用API来更新收藏状态
          // await api.toggleFavorite(storyId)
        }
      } catch (error) {
        console.error('Toggle favorite failed:', error)
        // 回滚状态
        const story = this.stories.find((s) => s.id === storyId)
        if (story) {
          story.is_favorite = !story.is_favorite
        }
      } finally {
        this.isFavoriteLoading = false
      }
    },

    clearCurrentStory() {
      this.currentStory = null
      this.currentActor = null
    },

    async fetchStories(categoryIds?: string[], sort?: string) {
      const filterCategoryIds = categoryIds?.filter((id) => id !== '')
      const currentParams = {
        categoryIds: filterCategoryIds || [],
        sort: sort || 'popular',
      }

      // 防重复请求：检查是否正在请求相同参数
      if (this.storiesLoading) {
        console.log('Stories request already in progress, skipping...')
        return
      }

      // 检查是否已有相同参数的数据
      if (
        this.lastFetchParams &&
        JSON.stringify(currentParams.categoryIds) ===
          JSON.stringify(this.lastFetchParams.categoryIds) &&
        currentParams.sort === this.lastFetchParams.sort &&
        this.stories.length > 0
      ) {
        console.log(
          'Stories data already exists for same parameters, skipping...',
        )
        return
      }

      this.storiesLoading = true
      this.loading = true
      this.error = null

      // 保存请求参数用于缓存比较
      this.lastFetchParams = currentParams

      try {
        const api = useApi()
        const result = await api.fetchStories({
          category_ids: filterCategoryIds,
          sort: sort || 'popular',
        })

        if (result.code === '0') {
          this.stories = result.data.stories || []
        } else {
          this.error = result.message
          this.stories = []
        }
      } catch (err) {
        this.error = 'Failed to load stories'
        this.stories = []
        console.error('Error fetching stories:', err)
      } finally {
        this.loading = false
        this.storiesLoading = false
      }
    },

    async fetchCategories() {
      // 防重复请求：检查是否正在请求或已有数据
      if (this.categoriesLoading) {
        console.log('Categories request already in progress, skipping...')
        return
      }

      if (this.storyCategories.length > 0) {
        console.log('Categories already loaded, skipping...')
        return
      }

      this.categoriesLoading = true

      try {
        const api = useApi()
        const result = await api.fetchCategories()

        if (result.code === '0') {
          const all = {
            id: 'all',
            name: 'All',
            parent_id: '',
            level: 0,
          }
          this.storyCategories = [all, ...result.data.category]
        } else {
          this.error = result.message
          this.storyCategories = []
        }
      } catch (err) {
        this.error = 'Failed to load categories'
        this.storyCategories = []
        console.error('Error fetching categories:', err)
      } finally {
        this.categoriesLoading = false
      }
    },

    reset() {
      this.currentStory = null
      this.currentActor = null
      this.stories = []
      this.loading = false
      this.error = null
      this.isFavoriteLoading = false
      this.lastFetchParams = undefined
    },
  },
})
